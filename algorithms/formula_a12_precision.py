"""
A12精準預測公式 - 基於A11測試結果的深度優化
目標：將預測誤差降低到9%以下

A11測試結果分析：
- 平均誤差39.6%，主要問題是預測值偏低
- 需要重新校準轉換係數和基礎參數
- 置信度因子工作正常(0.7-0.9)
- 權重分配需要調整
"""

import math
from typing import Dict, List
from formula_a11_ultimate import FormulaA11Ultimate, ExtendedPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory

class FormulaA12Precision(FormulaA11Ultimate):
    """A12精準預測公式 - 深度校準版本"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A12 - 精準預測版"
        self.version = "12.0"
        
        # 重新校準的基礎權重
        self.base_weights = {
            'kalman': 0.20,      # 降低Kalman權重
            'microbiome': 0.30,  # 增加微生物權重
            'metabolic': 0.30,   # 增加代謝權重
            'vascular': 0.20     # 保持血管權重
        }
        
        # 校準係數
        self.calibration_factors = {
            'base_multiplier': 2.8,      # 基礎放大係數
            'hba1c_amplifier': 1.5,      # HbA1c放大係數
            'bmi_amplifier': 1.3,        # BMI放大係數
            'age_dampener': 0.98,        # 年齡阻尼係數
            'carb_sensitivity': 1.2      # 碳水敏感係數
        }
    
    def calculate_precision_calibration(self, person: ExtendedPersonalProfile, 
                                      food: FoodComposition) -> float:
        """計算精準校準因子"""
        calibration = self.calibration_factors['base_multiplier']
        
        # HbA1c校準
        if person.hba1c > 7.0:
            hba1c_factor = 1.0 + (person.hba1c - 7.0) * self.calibration_factors['hba1c_amplifier']
            calibration *= hba1c_factor
        
        # BMI校準
        if person.bmi > 25.0:
            bmi_factor = 1.0 + (person.bmi - 25.0) * 0.05 * self.calibration_factors['bmi_amplifier']
            calibration *= bmi_factor
        
        # 年齡校準
        age_factor = self.calibration_factors['age_dampener'] ** (person.age - 30)
        calibration *= age_factor
        
        # 碳水化合物校準
        carb_factor = 1.0 + (food.total_carbs_g / 100.0) * self.calibration_factors['carb_sensitivity']
        calibration *= carb_factor
        
        # 糖尿病類型校準
        if person.diabetes_type == 2:
            calibration *= 1.2  # 2型糖尿病患者升糖反應更強
        
        return calibration
    
    def calculate_food_complexity_factor(self, food: FoodComposition) -> float:
        """計算食物複雜度因子"""
        complexity = 1.0
        
        # 纖維含量影響
        if food.fiber_g > 5:
            complexity *= 0.85  # 高纖維降低升糖
        elif food.fiber_g < 2:
            complexity *= 1.15   # 低纖維增加升糖
        
        # 脂肪含量影響
        if food.fat_g > 15:
            complexity *= 0.9   # 高脂肪延緩升糖
        
        # 蛋白質含量影響
        if food.protein_g > 20:
            complexity *= 0.95  # 高蛋白質緩解升糖
        
        # 加工程度影響
        if food.processing_level.value >= 3:  # 高度加工
            complexity *= 1.2
        elif food.processing_level.value <= 1:  # 最小加工
            complexity *= 0.9
        
        # 糖分含量影響
        sugar_ratio = food.sugar_g / max(food.total_carbs_g, 1)
        if sugar_ratio > 0.3:  # 高糖分
            complexity *= 1.3
        
        return complexity
    
    def calculate_metabolic_efficiency(self, person: ExtendedPersonalProfile) -> float:
        """計算代謝效率因子"""
        efficiency = 1.0
        
        # 胰島素敏感性
        if hasattr(person, 'insulin_sensitivity'):
            efficiency *= person.insulin_sensitivity
        
        # 胰島素分泌速率
        efficiency *= person.insulin_secretion_rate
        
        # 肝糖原狀態
        liver_factor = person.hepatic_glycogen_pct / 100.0
        efficiency *= (0.5 + liver_factor * 0.5)
        
        # 肌糖原狀態
        muscle_factor = person.muscle_glycogen_pct / 100.0
        efficiency *= (0.7 + muscle_factor * 0.3)
        
        # 微生物多樣性
        microbiome_factor = 0.6 + person.microbiome_diversity * 0.4
        efficiency *= microbiome_factor
        
        # 炎症狀態（負面影響）
        inflammation = (person.crp_level + person.il6_level + person.tnf_alpha) / 3.0
        inflammation_factor = 1.0 + (inflammation - 1.0) * 0.3
        efficiency /= inflammation_factor
        
        return efficiency
    
    def calculate_temporal_adjustment(self, context: MealContext, 
                                    meal_history: MealHistory) -> float:
        """計算時間相關調整因子"""
        adjustment = 1.0
        
        # 禁食時間的非線性影響
        fasting_hours = context.fasting_hours
        if fasting_hours > 8:
            # 長時間禁食增加升糖敏感性
            adjustment *= 1.0 + (fasting_hours - 8) * 0.08
        elif fasting_hours < 3:
            # 短時間禁食降低升糖反應
            adjustment *= 0.8 + fasting_hours * 0.067
        
        # 一日內餐次的累積效應
        recent_meals = len([m for m in meal_history.meals if m[0] > -8])  # 8小時內
        if recent_meals > 0:
            # 餐次越多，升糖反應越弱
            meal_factor = 1.0 - (recent_meals * 0.1)
            adjustment *= max(0.6, meal_factor)
        
        # 時間段的生理節律
        if context.time_of_day.value == 1:  # 早晨
            adjustment *= 1.15  # 晨間皮質醇高峰
        elif context.time_of_day.value == 4:  # 晚上
            adjustment *= 1.05  # 晚間胰島素敏感性下降
        
        return adjustment
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取A11的基礎預測
        base_result = super().calculate_comprehensive_glycemic_index(
            food, person, context, meal_history
        )
        
        # 應用精準校準
        precision_calibration = self.calculate_precision_calibration(person, food)
        food_complexity = self.calculate_food_complexity_factor(food)
        metabolic_efficiency = self.calculate_metabolic_efficiency(person)
        temporal_adjustment = self.calculate_temporal_adjustment(context, meal_history)
        
        # 計算最終校準因子
        total_calibration = (precision_calibration * 
                           food_complexity * 
                           temporal_adjustment / 
                           metabolic_efficiency)
        
        # 應用校準
        calibrated_gl = base_result['final_gl'] * total_calibration
        
        # 更新結果
        result = base_result.copy()
        result['final_gl'] = calibrated_gl
        result['glycemic_rate'] = calibrated_gl / result['peak_time_hours']
        
        # 重新評估安全性
        safety_threshold = self._calculate_dynamic_safety_threshold(person, context)
        result['is_safe'] = result['glycemic_rate'] <= safety_threshold
        result['risk_level'] = self._assess_comprehensive_risk(result['glycemic_rate'], safety_threshold, person)
        
        # 添加校準信息
        result['precision_calibration'] = precision_calibration
        result['food_complexity'] = food_complexity
        result['metabolic_efficiency'] = metabolic_efficiency
        result['temporal_adjustment'] = temporal_adjustment
        result['total_calibration'] = total_calibration
        
        return result
    
    def fine_tune_with_feedback(self, predicted_gl: float, actual_gl: float, 
                               person: ExtendedPersonalProfile, food: FoodComposition):
        """基於反饋進行微調"""
        error_ratio = actual_gl / max(predicted_gl, 1.0)
        
        # 如果預測偏低，增加相應的校準係數
        if error_ratio > 1.2:  # 實際值比預測高20%以上
            if person.hba1c > 7.0:
                self.calibration_factors['hba1c_amplifier'] *= 1.05
            if person.bmi > 25.0:
                self.calibration_factors['bmi_amplifier'] *= 1.05
            if food.total_carbs_g > 50:
                self.calibration_factors['carb_sensitivity'] *= 1.03
        
        # 如果預測偏高，降低相應的校準係數
        elif error_ratio < 0.8:  # 實際值比預測低20%以上
            if person.hba1c > 7.0:
                self.calibration_factors['hba1c_amplifier'] *= 0.98
            if person.bmi > 25.0:
                self.calibration_factors['bmi_amplifier'] *= 0.98
            if food.total_carbs_g > 50:
                self.calibration_factors['carb_sensitivity'] *= 0.99
        
        # 更新基礎放大係數
        if 0.9 <= error_ratio <= 1.1:  # 預測很準確
            pass  # 保持當前係數
        else:
            adjustment = min(max(error_ratio, 0.5), 2.0)  # 限制調整範圍
            self.calibration_factors['base_multiplier'] *= (1.0 + (adjustment - 1.0) * 0.1)
        
        # 確保係數在合理範圍內
        for key in self.calibration_factors:
            self.calibration_factors[key] = max(0.5, min(5.0, self.calibration_factors[key]))
    
    def get_calibration_status(self) -> Dict[str, float]:
        """獲取當前校準狀態"""
        return {
            'calibration_factors': self.calibration_factors.copy(),
            'base_weights': self.base_weights.copy(),
            'total_predictions': len(self.prediction_history),
            'avg_recent_prediction': sum(self.prediction_history[-10:]) / min(len(self.prediction_history), 10) if self.prediction_history else 0
        }
