"""
升糖基準指數計算工具
基於最終突破公式的實用計算器
"""

from final_breakthrough_formula import FinalBreakthroughFormula, PracticalPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod
from typing import Dict, List

class GlycemicCalculatorTool:
    """升糖基準指數計算工具"""
    
    def __init__(self):
        self.formula = FinalBreakthroughFormula()
        self.meal_history = MealHistory()
        
    def create_personal_profile(self, user_data: Dict) -> PracticalPersonalProfile:
        """創建個人資料"""
        return PracticalPersonalProfile(
            # 基本生理參數
            age=user_data.get('age', 35),
            height_cm=user_data.get('height_cm', 170.0),
            weight_kg=user_data.get('weight_kg', 70.0),
            gender=user_data.get('gender', 'M'),
            
            # 健康指標
            hba1c=user_data.get('hba1c', 5.4),
            blood_pressure_sys=user_data.get('blood_pressure_sys', 120),
            blood_pressure_dia=user_data.get('blood_pressure_dia', 80),
            resting_hr=user_data.get('resting_hr', 70),
            
            # 生活習慣
            exercise_frequency=user_data.get('exercise_frequency', 3),
            sleep_hours=user_data.get('sleep_hours', 7.0),
            stress_level=user_data.get('stress_level', 3),
            smoking=user_data.get('smoking', False),
            alcohol_frequency=user_data.get('alcohol_frequency', 0),
            
            # 家族史
            family_diabetes_history=user_data.get('family_diabetes_history', False),
            family_heart_disease=user_data.get('family_heart_disease', False),
            
            # 用餐習慣
            eating_speed=user_data.get('eating_speed', 1.0),
            meal_frequency=user_data.get('meal_frequency', 3),
            chewing_thoroughly=user_data.get('chewing_thoroughly', True),
            water_intake_liters=user_data.get('water_intake_liters', 2.0),
            
            # 當前狀態
            current_stress=user_data.get('current_stress', 3),
            fatigue_level=user_data.get('fatigue_level', 2),
            sleep_quality_last_night=user_data.get('sleep_quality_last_night', 3)
        )
    
    def create_food_composition(self, food_data: Dict) -> FoodComposition:
        """創建食物組成"""
        # 碳水化合物類型映射
        carb_type_map = {
            '簡單糖': CarbType.SIMPLE_SUGAR,
            '雙糖': CarbType.DISACCHARIDE,
            '澱粉': CarbType.STARCH,
            '複合碳水': CarbType.COMPLEX_CARB,
            '抗性澱粉': CarbType.RESISTANT_STARCH
        }
        
        # 加工程度映射
        processing_map = {
            '最小': ProcessingLevel.MINIMAL,
            '輕度': ProcessingLevel.LIGHT,
            '中度': ProcessingLevel.MODERATE,
            '高度': ProcessingLevel.HIGHLY
        }
        
        # 烹飪方式映射
        cooking_map = {
            '生食': CookingMethod.RAW,
            '水煮': CookingMethod.BOILED,
            '蒸煮': CookingMethod.STEAMED,
            '烘烤': CookingMethod.BAKED,
            '油炸': CookingMethod.FRIED,
            '燒烤': CookingMethod.GRILLED
        }
        
        return FoodComposition(
            carb_type=carb_type_map.get(food_data.get('carb_type', '澱粉'), CarbType.STARCH),
            total_carbs_g=food_data.get('total_carbs_g', 50.0),
            fiber_g=food_data.get('fiber_g', 2.0),
            protein_g=food_data.get('protein_g', 10.0),
            fat_g=food_data.get('fat_g', 5.0),
            processing_level=processing_map.get(food_data.get('processing_level', '中度'), ProcessingLevel.MODERATE),
            cooking_method=cooking_map.get(food_data.get('cooking_method', '水煮'), CookingMethod.BOILED),
            sugar_g=food_data.get('sugar_g', 5.0),
            starch_g=food_data.get('starch_g', 45.0),
            water_content_pct=food_data.get('water_content_pct', 60.0),
            viscosity=food_data.get('viscosity', 1.0)
        )
    
    def create_meal_context(self, context_data: Dict) -> MealContext:
        """創建用餐情境"""
        time_map = {
            '早餐': TimeOfDay.MORNING,
            '午餐': TimeOfDay.MIDDAY,
            '晚餐': TimeOfDay.EVENING,
            '點心': TimeOfDay.MIDDAY
        }
        
        return MealContext(
            time_of_day=time_map.get(context_data.get('meal_time', '午餐'), TimeOfDay.MIDDAY),
            fasting_hours=context_data.get('fasting_hours', 4.0),
            stress_level=context_data.get('stress_level', 3)
        )
    
    def calculate_glycemic_impact(self, user_data: Dict, food_data: Dict, context_data: Dict) -> Dict:
        """計算升糖影響"""
        # 創建個人資料
        person = self.create_personal_profile(user_data)
        
        # 創建食物組成
        food = self.create_food_composition(food_data)
        
        # 創建用餐情境
        context = self.create_meal_context(context_data)
        
        # 計算升糖指數
        result = self.formula.calculate_comprehensive_glycemic_index(
            food, person, context, self.meal_history
        )
        
        # 更新餐食歷史
        self.meal_history.add_meal(0, result['final_gl'])
        
        # 格式化結果
        formatted_result = {
            '升糖負荷': round(result['final_gl'], 1),
            '預測血糖變化_mg_dL': round(result['predicted_ppg_change'], 1),
            '升糖速率': round(result['glycemic_rate'], 1),
            '峰值時間_小時': round(result['peak_time_hours'], 1),
            '風險等級': result['risk_level'],
            '是否安全': result['is_safe'],
            '安全閾值': round(result['safety_threshold'], 1),
            '置信度': f"{result['confidence_score']:.0%}",
            '個人因子': round(result['personal_factor'], 3),
            '生活因子': round(result['lifestyle_factor'], 3),
            '情境因子': round(result['context_factor'], 3),
            '基礎係數': round(result['base_multiplier'], 3)
        }
        
        return formatted_result
    
    def get_risk_recommendations(self, result: Dict) -> List[str]:
        """獲取風險建議"""
        recommendations = []
        
        risk_level = result['風險等級']
        gl_value = result['升糖負荷']
        glycemic_rate = result['升糖速率']
        
        if risk_level == '極低風險':
            recommendations.append("✅ 可以安心食用")
            recommendations.append("💡 這是很好的食物選擇")
        elif risk_level == '低風險':
            recommendations.append("✅ 適量食用")
            recommendations.append("💡 建議搭配蔬菜一起食用")
        elif risk_level == '中風險':
            recommendations.append("⚠️ 謹慎食用，建議減少份量")
            recommendations.append("🚶 餐後30分鐘進行輕度運動")
            recommendations.append("📊 建議監測血糖變化")
        elif risk_level == '高風險':
            recommendations.append("🚨 建議避免食用或大幅減量")
            recommendations.append("🏃 如已食用，建議立即進行中等強度運動")
            recommendations.append("📱 密切監測血糖變化")
            recommendations.append("👨‍⚕️ 考慮諮詢醫療專業人員")
        else:  # 危險
            recommendations.append("🛑 強烈建議避免食用")
            recommendations.append("🚨 如已食用，立即聯繫醫療專業人員")
            recommendations.append("📞 準備緊急處理措施")
        
        # 根據升糖速率添加建議
        if glycemic_rate > 15:
            recommendations.append("⚡ 升糖速度很快，需要特別注意")
        elif glycemic_rate > 10:
            recommendations.append("📈 升糖速度較快，建議配合運動")
        
        # 根據GL值添加建議
        if gl_value > 20:
            recommendations.append(f"📊 升糖負荷較高 ({gl_value})，建議減少份量")
        
        return recommendations
    
    def get_food_alternatives(self, food_data: Dict) -> List[Dict]:
        """獲取食物替代建議"""
        carb_type = food_data.get('carb_type', '澱粉')
        
        alternatives = {
            '澱粉': [
                {'name': '糙米飯', 'reason': '纖維含量更高，升糖較慢'},
                {'name': '燕麥', 'reason': '豐富纖維，穩定血糖'},
                {'name': '藜麥', 'reason': '完整蛋白質，低升糖'},
                {'name': '地瓜', 'reason': '天然甜味，營養豐富'}
            ],
            '簡單糖': [
                {'name': '蘋果', 'reason': '天然果糖，含纖維'},
                {'name': '莓果類', 'reason': '抗氧化物豐富，低糖'},
                {'name': '堅果', 'reason': '健康脂肪，穩定血糖'},
                {'name': '酪梨', 'reason': '單元不飽和脂肪酸'}
            ],
            '複合碳水': [
                {'name': '全麥麵包', 'reason': '完整穀物，營養價值高'},
                {'name': '豆類', 'reason': '蛋白質豐富，升糖緩慢'},
                {'name': '蔬菜', 'reason': '低熱量，高纖維'},
                {'name': '堅果種子', 'reason': '健康脂肪和蛋白質'}
            ]
        }
        
        return alternatives.get(carb_type, [])
    
    def generate_meal_plan_suggestions(self, user_data: Dict, target_gl: float = 15.0) -> Dict:
        """生成餐食計劃建議"""
        person = self.create_personal_profile(user_data)
        
        # 基於個人特徵調整目標GL
        if person.hba1c > 8.0:
            target_gl = 10.0  # 更嚴格控制
        elif person.hba1c < 6.0:
            target_gl = 20.0  # 可以稍微寬鬆
        
        suggestions = {
            '每日目標GL': target_gl,
            '早餐建議': [
                '燕麥粥 + 堅果 + 莓果',
                '全麥吐司 + 酪梨 + 水煮蛋',
                '希臘優格 + 奇亞籽 + 少量水果'
            ],
            '午餐建議': [
                '糙米飯 + 烤雞胸 + 大量蔬菜',
                '藜麥沙拉 + 豆腐 + 橄欖油醋汁',
                '全麥麵條 + 瘦肉 + 蔬菜湯'
            ],
            '晚餐建議': [
                '烤魚 + 地瓜 + 綠色蔬菜',
                '豆類燉菜 + 少量糙米',
                '雞肉沙拉 + 堅果 + 橄欖油'
            ],
            '點心建議': [
                '一小把堅果',
                '蘋果片 + 杏仁醬',
                '胡蘿蔔條 + 鷹嘴豆泥'
            ]
        }
        
        return suggestions

def main():
    """主函數 - 示例使用"""
    calculator = GlycemicCalculatorTool()
    
    # 示例用戶資料
    user_data = {
        'age': 50,
        'height_cm': 170,
        'weight_kg': 75,
        'gender': 'M',
        'hba1c': 6.5,
        'blood_pressure_sys': 130,
        'blood_pressure_dia': 85,
        'resting_hr': 75,
        'exercise_frequency': 3,
        'sleep_hours': 7.0,
        'stress_level': 3,
        'family_diabetes_history': False
    }
    
    # 示例食物資料
    food_data = {
        'carb_type': '澱粉',
        'total_carbs_g': 60.0,
        'fiber_g': 1.0,
        'protein_g': 8.0,
        'fat_g': 1.0,
        'sugar_g': 0.0,
        'starch_g': 59.0,
        'processing_level': '中度',
        'cooking_method': '水煮'
    }
    
    # 示例用餐情境
    context_data = {
        'meal_time': '午餐',
        'fasting_hours': 4.0,
        'stress_level': 3
    }
    
    # 計算升糖影響
    result = calculator.calculate_glycemic_impact(user_data, food_data, context_data)
    
    print("=== 升糖基準指數計算結果 ===")
    for key, value in result.items():
        print(f"{key}: {value}")
    
    print("\n=== 風險建議 ===")
    recommendations = calculator.get_risk_recommendations(result)
    for rec in recommendations:
        print(rec)
    
    print("\n=== 食物替代建議 ===")
    alternatives = calculator.get_food_alternatives(food_data)
    for alt in alternatives:
        print(f"• {alt['name']}: {alt['reason']}")
    
    print("\n=== 餐食計劃建議 ===")
    meal_plan = calculator.generate_meal_plan_suggestions(user_data)
    for category, suggestions in meal_plan.items():
        print(f"\n{category}:")
        if isinstance(suggestions, list):
            for suggestion in suggestions:
                print(f"  • {suggestion}")
        else:
            print(f"  {suggestions}")

if __name__ == "__main__":
    main()
