"""
最終突破升糖基準指數公式
目標：突破10%誤差壁壘

基於平衡精準公式的成功經驗：
- 驗證階段已達到12.1%誤差，非常接近目標
- 需要進一步微調來突破10%
- 使用更精細的個人化調整
"""

from balanced_precise_formula import BalancedPreciseFormula, PracticalPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, CarbType, TimeOfDay
from typing import Dict

class FinalBreakthroughFormula(BalancedPreciseFormula):
    """最終突破公式 - 基於成功經驗的微調版本"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "最終突破版"
        self.version = "Breakthrough"
        
        # 基於成功經驗的優化係數
        self.breakthrough_calibration = {
            'base_multiplier': 2.2,      # 基於學習結果的最佳值
            'carb_efficiency': 0.85,     # 稍微提高碳水效率
            'personal_impact': 0.95,     # 增強個人因子影響
            'lifestyle_impact': 0.88,    # 增強生活習慣影響
        }
        
        # 優化的轉換係數
        self.breakthrough_conversion = 2.8  # 基於學習結果的最佳值
        
        # 精細化調整參數
        self.fine_tuning = {
            'hba1c_precision': 1.1,      # HbA1c精細調整
            'bmi_precision': 1.05,       # BMI精細調整
            'age_precision': 1.02,       # 年齡精細調整
            'exercise_boost': 1.15,      # 運動保護增強
            'sleep_impact': 1.08         # 睡眠影響增強
        }
        
        # 學習參數
        self.fine_learning_rate = 0.05  # 更小的學習率
        self.convergence_threshold = 0.02  # 收斂閾值
    
    def calculate_breakthrough_personal_factor(self, person: PracticalPersonalProfile) -> float:
        """計算突破性個人因子"""
        factor = 1.0
        
        # 精細化HbA1c影響
        if person.hba1c > 8.0:
            hba1c_factor = 1.0 + (person.hba1c - 8.0) * 0.18 * self.fine_tuning['hba1c_precision']
        elif person.hba1c > 7.0:
            hba1c_factor = 1.0 + (person.hba1c - 7.0) * 0.12 * self.fine_tuning['hba1c_precision']
        elif person.hba1c > 6.0:
            hba1c_factor = 1.0 + (person.hba1c - 6.0) * 0.06 * self.fine_tuning['hba1c_precision']
        else:
            hba1c_factor = 1.0
        
        factor *= hba1c_factor
        
        # 精細化BMI影響
        if person.bmi > 30:
            bmi_factor = 1.0 + (person.bmi - 30) * 0.045 * self.fine_tuning['bmi_precision']
        elif person.bmi > 25:
            bmi_factor = 1.0 + (person.bmi - 25) * 0.025 * self.fine_tuning['bmi_precision']
        elif person.bmi < 20:
            bmi_factor = 0.95  # 低BMI保護
        else:
            bmi_factor = 1.0
        
        factor *= bmi_factor
        
        # 精細化年齡影響
        if person.age > 65:
            age_factor = 1.0 + (person.age - 65) * 0.015 * self.fine_tuning['age_precision']
        elif person.age > 50:
            age_factor = 1.0 + (person.age - 50) * 0.008 * self.fine_tuning['age_precision']
        elif person.age < 30:
            age_factor = 0.95  # 年輕人保護
        else:
            age_factor = 1.0
        
        factor *= age_factor
        
        return factor * self.breakthrough_calibration['personal_impact']
    
    def calculate_breakthrough_lifestyle_factor(self, person: PracticalPersonalProfile) -> float:
        """計算突破性生活習慣因子"""
        factor = 1.0
        
        # 增強運動保護效應
        if person.exercise_frequency >= 5:
            factor *= 0.75 * self.fine_tuning['exercise_boost']  # 強化保護
        elif person.exercise_frequency >= 3:
            factor *= 0.85 * self.fine_tuning['exercise_boost']
        elif person.exercise_frequency >= 1:
            factor *= 0.95
        else:
            factor *= 1.25  # 不運動懲罰
        
        # 增強睡眠影響
        if person.sleep_hours < 5:
            factor *= 1.25 * self.fine_tuning['sleep_impact']  # 嚴重睡眠不足
        elif person.sleep_hours < 6:
            factor *= 1.18 * self.fine_tuning['sleep_impact']
        elif person.sleep_hours > 9:
            factor *= 0.92  # 過度睡眠輕微影響
        elif person.sleep_hours >= 7:
            factor *= 0.93  # 充足睡眠保護
        
        # 精細化壓力影響
        stress_impact = 1.0 + (person.stress_level - 3) * 0.08
        factor *= stress_impact
        
        # 血壓的精細影響
        if person.blood_pressure_sys > 150:
            factor *= 1.08
        elif person.blood_pressure_sys > 140:
            factor *= 1.04
        elif person.blood_pressure_sys < 110:
            factor *= 0.96  # 低血壓保護
        
        # 心率的精細影響
        if person.resting_hr > 90:
            factor *= 1.05
        elif person.resting_hr > 80:
            factor *= 1.02
        elif person.resting_hr < 55:
            factor *= 0.92  # 運動員心率保護
        elif person.resting_hr < 65:
            factor *= 0.96
        
        return factor * self.breakthrough_calibration['lifestyle_impact']
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: PracticalPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 使用優化的基礎GL計算
        base_gl = self.calculate_balanced_base_gl(food)
        
        # 使用突破性因子
        personal_factor = self.calculate_breakthrough_personal_factor(person)
        lifestyle_factor = self.calculate_breakthrough_lifestyle_factor(person)
        
        # 精細化餐食情境調整
        context_factor = 1.0
        if context.fasting_hours > 10:
            context_factor *= 1.12  # 長時間禁食
        elif context.fasting_hours > 8:
            context_factor *= 1.06
        elif context.fasting_hours < 2:
            context_factor *= 0.92  # 短時間禁食
        elif context.fasting_hours < 4:
            context_factor *= 0.96
        
        # 時間段的精細影響
        if context.time_of_day == TimeOfDay.MORNING:
            context_factor *= 1.08  # 晨間胰島素抵抗
        elif context.time_of_day == TimeOfDay.EVENING:
            context_factor *= 1.03  # 晚間輕微影響
        
        # 計算最終GL
        final_gl = (base_gl * 
                   personal_factor * 
                   lifestyle_factor * 
                   context_factor * 
                   self.breakthrough_calibration['base_multiplier'])
        
        # 計算其他指標
        peak_time = self._estimate_peak_time(food, person)
        glycemic_rate = final_gl / peak_time
        predicted_ppg_change = final_gl * self.breakthrough_conversion
        
        # 安全評估
        safety_threshold = self._calculate_practical_safety_threshold(person)
        is_safe = glycemic_rate <= safety_threshold
        risk_level = self._assess_practical_risk(glycemic_rate, safety_threshold, person)
        
        return {
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'peak_time_hours': peak_time,
            'predicted_ppg_change': predicted_ppg_change,
            'is_safe': is_safe,
            'risk_level': risk_level,
            'safety_threshold': safety_threshold,
            'confidence_score': 0.92,
            'base_gl': base_gl,
            'personal_factor': personal_factor,
            'lifestyle_factor': lifestyle_factor,
            'context_factor': context_factor,
            'base_multiplier': self.breakthrough_calibration['base_multiplier']
        }
    
    def precision_calibrate(self, predicted_ppg: float, actual_ppg: float):
        """精準校準 - 更細緻的調整"""
        error_ratio = predicted_ppg / max(actual_ppg, 1.0)
        error_magnitude = abs(error_ratio - 1.0)
        
        # 根據誤差大小決定調整策略
        if error_magnitude < self.convergence_threshold:
            # 已經很接近，只做微調
            adjustment_rate = self.fine_learning_rate * 0.5
        elif error_magnitude < 0.1:  # 誤差<10%
            adjustment_rate = self.fine_learning_rate
        elif error_magnitude < 0.2:  # 誤差<20%
            adjustment_rate = self.fine_learning_rate * 1.5
        else:  # 誤差≥20%
            adjustment_rate = self.fine_learning_rate * 2
        
        # 精細調整主要係數
        if error_ratio > 1.05:  # 預測偏高5%以上
            self.breakthrough_calibration['base_multiplier'] *= (1 - adjustment_rate)
            self.breakthrough_conversion *= (1 - adjustment_rate * 0.8)
        elif error_ratio < 0.95:  # 預測偏低5%以上
            self.breakthrough_calibration['base_multiplier'] *= (1 + adjustment_rate)
            self.breakthrough_conversion *= (1 + adjustment_rate * 0.8)
        
        # 基於實際值範圍微調精細參數
        if actual_ppg > 120:  # 高血糖反應
            self.fine_tuning['hba1c_precision'] *= 1.01
            self.fine_tuning['bmi_precision'] *= 1.01
        elif actual_ppg < 80:  # 低血糖反應
            self.fine_tuning['exercise_boost'] *= 1.01
            self.fine_tuning['sleep_impact'] *= 0.99
        
        # 限制所有係數範圍
        self.breakthrough_calibration['base_multiplier'] = max(0.5, min(4.0, self.breakthrough_calibration['base_multiplier']))
        self.breakthrough_conversion = max(1.0, min(6.0, self.breakthrough_conversion))
        
        for key in self.fine_tuning:
            self.fine_tuning[key] = max(0.8, min(1.3, self.fine_tuning[key]))
    
    def get_breakthrough_status(self) -> Dict:
        """獲取突破狀態"""
        return {
            'breakthrough_calibration': self.breakthrough_calibration.copy(),
            'breakthrough_conversion': self.breakthrough_conversion,
            'fine_tuning': self.fine_tuning.copy()
        }
