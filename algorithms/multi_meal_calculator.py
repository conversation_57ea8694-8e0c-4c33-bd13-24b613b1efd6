"""
多餐干預升糖計算器
實用工具：支援份量計算、營養干預、多餐效應
"""

from multi_meal_intervention_system import *
from datetime import datetime, timedelta

class MultiMealCalculator:
    """多餐干預計算器"""
    
    def __init__(self):
        self.system = MultiMealGlycemicSystem()
        self.current_daily_plan = DailyMealPlan(date=datetime.now().date())
    
    def calculate_food_portions(self, food_name: str, weight_g: float) -> Dict:
        """計算食物份數"""
        try:
            portions, nutrition = self.system.food_db.calculate_portions(food_name, weight_g)
            
            return {
                'food_name': food_name,
                'weight_g': weight_g,
                'portions': round(portions, 2),
                'nutrition': {
                    'carbs': round(nutrition['carbs'], 1),
                    'protein': round(nutrition['protein'], 1),
                    'fat': round(nutrition['fat'], 1),
                    'fiber': round(nutrition['fiber'], 1)
                },
                'portion_description': self._describe_portions(portions)
            }
        except ValueError as e:
            return {'error': str(e)}
    
    def _describe_portions(self, portions: float) -> str:
        """描述份數"""
        if portions < 0.5:
            return f"不足半份 ({portions:.2f}份)"
        elif portions < 1.0:
            return f"約半份 ({portions:.2f}份)"
        elif portions < 1.5:
            return f"約1份 ({portions:.2f}份)"
        elif portions < 2.0:
            return f"約1.5份 ({portions:.2f}份)"
        else:
            return f"約{portions:.1f}份"
    
    def design_intervention_strategy(self, base_carbs: float, target_reduction: float = 0.3) -> List[Dict]:
        """設計干預策略"""
        strategies = []
        
        # 策略1：蛋白質干預
        protein_needed = base_carbs * 0.3  # 蛋白質/碳水 = 0.3
        strategies.append({
            'name': '蛋白質干預',
            'intervention': NutritionIntervention(InterventionType.PROTEIN, protein_needed, 'with'),
            'description': f"添加 {protein_needed:.1f}g 蛋白質（如雞胸肉 {protein_needed*4.3:.0f}g）",
            'expected_reduction': min(0.4, protein_needed * 0.015)
        })
        
        # 策略2：纖維干預
        fiber_needed = min(15, base_carbs * 0.2)
        strategies.append({
            'name': '纖維干預',
            'intervention': NutritionIntervention(InterventionType.FIBER, fiber_needed, 'before'),
            'description': f"餐前攝取 {fiber_needed:.1f}g 纖維（如蔬菜 {fiber_needed*40:.0f}g）",
            'expected_reduction': min(0.5, fiber_needed * 0.025)
        })
        
        # 策略3：不飽和脂肪干預
        fat_needed = min(20, base_carbs * 0.25)
        strategies.append({
            'name': '不飽和脂肪干預',
            'intervention': NutritionIntervention(InterventionType.UNSATURATED_FAT, fat_needed, 'with'),
            'description': f"添加 {fat_needed:.1f}g 不飽和脂肪（如堅果 {fat_needed*1.6:.0f}g）",
            'expected_reduction': min(0.3, fat_needed * 0.012)
        })
        
        # 策略4：醋干預
        strategies.append({
            'name': '醋干預',
            'intervention': NutritionIntervention(InterventionType.VINEGAR, 15, 'before'),
            'description': "餐前15分鐘飲用15ml蘋果醋（稀釋後）",
            'expected_reduction': 0.15
        })
        
        # 策略5：複合干預
        combo_protein = base_carbs * 0.2
        combo_fiber = min(10, base_carbs * 0.15)
        combo_fat = min(15, base_carbs * 0.2)
        
        combo_interventions = [
            NutritionIntervention(InterventionType.PROTEIN, combo_protein, 'with'),
            NutritionIntervention(InterventionType.FIBER, combo_fiber, 'before'),
            NutritionIntervention(InterventionType.UNSATURATED_FAT, combo_fat, 'with')
        ]
        
        combo_reduction = self.system.intervention_calc.calculate_intervention_effect(
            combo_interventions, base_carbs
        )
        
        strategies.append({
            'name': '複合干預',
            'interventions': combo_interventions,
            'description': f"組合策略：蛋白質{combo_protein:.1f}g + 纖維{combo_fiber:.1f}g + 不飽和脂肪{combo_fat:.1f}g",
            'expected_reduction': combo_reduction
        })
        
        return strategies
    
    def calculate_meal_with_interventions(self, food_weights: Dict[str, float],
                                        interventions: List[NutritionIntervention],
                                        person_profile: Dict,
                                        meal_time: datetime = None) -> Dict:
        """計算含干預的餐食影響"""
        if meal_time is None:
            meal_time = datetime.now()
        
        # 預測餐食影響
        prediction = self.system.predict_meal_impact(
            food_weights=food_weights,
            interventions=interventions,
            person_profile=person_profile,
            meal_time=meal_time,
            daily_plan=self.current_daily_plan
        )
        
        # 計算無干預的基準
        baseline_prediction = self.system.predict_meal_impact(
            food_weights=food_weights,
            interventions=[],
            person_profile=person_profile,
            meal_time=meal_time,
            daily_plan=self.current_daily_plan
        )
        
        # 計算干預效果
        intervention_benefit = {
            'gl_reduction': baseline_prediction['predicted_gl'] - prediction['predicted_gl'],
            'bg_reduction': baseline_prediction['predicted_bg_change'] - prediction['predicted_bg_change'],
            'reduction_percentage': (baseline_prediction['predicted_gl'] - prediction['predicted_gl']) / baseline_prediction['predicted_gl'] * 100
        }
        
        # 添加到每日計劃
        meal_record = MealRecord(
            meal_time=meal_time,
            food_portions={name: weight for name, weight in food_weights.items()},
            interventions=interventions,
            predicted_gl=prediction['predicted_gl']
        )
        self.current_daily_plan.meals.append(meal_record)
        
        return {
            'prediction': prediction,
            'baseline': baseline_prediction,
            'intervention_benefit': intervention_benefit,
            'meal_record': meal_record
        }
    
    def get_daily_summary(self) -> Dict:
        """獲取每日總結"""
        total_gl = sum(meal.predicted_gl for meal in self.current_daily_plan.meals)
        total_interventions = sum(len(meal.interventions) for meal in self.current_daily_plan.meals)
        
        # 計算風險分布
        risk_distribution = {}
        for meal in self.current_daily_plan.meals:
            # 簡化風險評估
            if meal.predicted_gl < 8:
                risk = "低風險"
            elif meal.predicted_gl < 15:
                risk = "中風險"
            else:
                risk = "高風險"
            
            risk_distribution[risk] = risk_distribution.get(risk, 0) + 1
        
        return {
            'date': self.current_daily_plan.date,
            'total_meals': len(self.current_daily_plan.meals),
            'total_gl': round(total_gl, 1),
            'avg_gl_per_meal': round(total_gl / max(len(self.current_daily_plan.meals), 1), 1),
            'total_interventions': total_interventions,
            'risk_distribution': risk_distribution,
            'recommendations': self._generate_daily_recommendations(total_gl)
        }
    
    def _generate_daily_recommendations(self, total_gl: float) -> List[str]:
        """生成每日建議"""
        recommendations = []
        
        if total_gl > 60:
            recommendations.append("🚨 每日總升糖負荷過高，建議減少碳水攝取或增加干預")
        elif total_gl > 40:
            recommendations.append("⚠️ 每日升糖負荷偏高，建議增加運動或營養干預")
        else:
            recommendations.append("✅ 每日升糖負荷控制良好")
        
        meal_count = len(self.current_daily_plan.meals)
        if meal_count > 5:
            recommendations.append("💡 餐次較多，建議控制每餐份量")
        elif meal_count < 3:
            recommendations.append("💡 餐次較少，注意避免過度飢餓")
        
        return recommendations
    
    def reset_daily_plan(self, date: datetime = None):
        """重置每日計劃"""
        if date is None:
            date = datetime.now().date()
        self.current_daily_plan = DailyMealPlan(date=date)
    
    def get_available_foods(self) -> Dict[str, Dict]:
        """獲取可用食物列表"""
        foods_by_category = {
            '主食類': {},
            '蛋白質類': {},
            '蔬菜類': {},
            '水果類': {},
            '油脂類': {}
        }
        
        category_mapping = {
            '白米飯': '主食類', '糙米飯': '主食類', '白麵條': '主食類', 
            '全麥麵包': '主食類', '燕麥': '主食類',
            '雞胸肉': '蛋白質類', '魚肉': '蛋白質類', '豆腐': '蛋白質類', '雞蛋': '蛋白質類',
            '青菜': '蔬菜類', '花椰菜': '蔬菜類', '胡蘿蔔': '蔬菜類',
            '蘋果': '水果類', '香蕉': '水果類', '葡萄': '水果類',
            '橄欖油': '油脂類', '堅果': '油脂類'
        }
        
        for food_name, portion in self.system.food_db.portions.items():
            category = category_mapping.get(food_name, '其他')
            if category in foods_by_category:
                foods_by_category[category][food_name] = {
                    'standard_weight': portion.standard_weight_g,
                    'carbs_per_portion': portion.carbs_per_portion,
                    'protein_per_portion': portion.protein_per_portion,
                    'fat_per_portion': portion.fat_per_portion,
                    'fiber_per_portion': portion.fiber_per_portion
                }
        
        return foods_by_category

def demo_usage():
    """示例使用"""
    calculator = MultiMealCalculator()
    
    print("=== 多餐干預升糖計算器示例 ===\n")
    
    # 1. 查看可用食物
    print("1. 可用食物列表：")
    foods = calculator.get_available_foods()
    for category, food_list in foods.items():
        print(f"\n{category}:")
        for food_name, info in food_list.items():
            print(f"  • {food_name}: 標準份量 {info['standard_weight']}g")
    
    # 2. 計算食物份數
    print(f"\n2. 食物份數計算示例：")
    portion_result = calculator.calculate_food_portions("白米飯", 120)
    print(f"120g白米飯 = {portion_result}")
    
    # 3. 設計干預策略
    print(f"\n3. 干預策略設計（針對45g碳水）：")
    strategies = calculator.design_intervention_strategy(45, 0.3)
    for strategy in strategies:
        print(f"• {strategy['name']}: {strategy['description']}")
        print(f"  預期降低: {strategy['expected_reduction']:.1%}")
    
    # 4. 計算含干預的餐食
    print(f"\n4. 餐食計算示例：")
    person_profile = {
        'age': 45, 'bmi': 26.0, 'hba1c': 6.8, 'exercise_frequency': 3
    }
    
    food_weights = {'白米飯': 110, '雞胸肉': 60, '青菜': 150}
    interventions = [
        NutritionIntervention(InterventionType.PROTEIN, 14, 'with'),
        NutritionIntervention(InterventionType.FIBER, 6, 'before')
    ]
    
    result = calculator.calculate_meal_with_interventions(
        food_weights, interventions, person_profile
    )
    
    print(f"無干預預測: GL={result['baseline']['predicted_gl']:.1f}, 血糖變化={result['baseline']['predicted_bg_change']:.1f}mg/dL")
    print(f"含干預預測: GL={result['prediction']['predicted_gl']:.1f}, 血糖變化={result['prediction']['predicted_bg_change']:.1f}mg/dL")
    print(f"干預效果: 降低{result['intervention_benefit']['reduction_percentage']:.1f}%")
    
    # 5. 每日總結
    print(f"\n5. 每日總結：")
    summary = calculator.get_daily_summary()
    print(f"總餐數: {summary['total_meals']}")
    print(f"總GL: {summary['total_gl']}")
    print(f"平均每餐GL: {summary['avg_gl_per_meal']}")
    print(f"建議: {summary['recommendations']}")

if __name__ == "__main__":
    demo_usage()
