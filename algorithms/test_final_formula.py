"""
最終優化公式測試
目標：通過深度校準達到9%以下的預測誤差
"""

from final_optimized_formula import FinalOptimizedFormula
from test_practical_formulas import create_practical_test_person
from formula_validation_test import ClinicalDataValidator
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

# 使用純Python實現統計函數
def np_mean(data):
    return sum(data) / len(data) if data else 0

def test_final_formula_with_learning():
    """測試最終公式的學習能力"""
    print("=== 最終優化公式學習測試 ===\n")
    print("使用真實臨床數據進行漸進學習")
    print("目標：達到9%以下的預測誤差\n")
    
    formula = FinalOptimizedFormula()
    validator = ClinicalDataValidator()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0,
        water_content_pct=65.0,
        viscosity=1.5
    )
    
    meal_history = MealHistory()
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    errors = []
    learning_progress = []
    
    print("開始漸進學習過程...")
    print("-" * 60)
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"學習步驟 {i+1}: 患者年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建個人資料
        person = create_practical_test_person(patient_data)
        
        # 預測
        result = formula.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        errors.append(error)
        
        print(f"  實際: {actual_ppg:.1f} mg/dL, 預測: {predicted_ppg:.1f} mg/dL, 誤差: {error:.1f}%")
        print(f"  校準因子: {result['total_calibration']:.3f}")
        
        # 學習更新
        formula.learn_from_feedback(predicted_ppg, actual_ppg, person, standard_meal)
        
        # 記錄學習進度
        stats = formula.get_performance_stats()
        learning_progress.append({
            'step': i+1,
            'error': error,
            'avg_error': stats['avg_error'],
            'base_multiplier': stats['current_coefficients']['base_multiplier'],
            'gl_factor': stats['current_gl_factor']
        })
        
        print(f"  當前平均誤差: {stats['avg_error']:.1f}%")
        print(f"  基礎係數: {stats['current_coefficients']['base_multiplier']:.3f}")
        print()
    
    # 分析學習結果
    final_avg_error = np_mean(errors)
    accurate_predictions = sum(1 for e in errors if e <= 9.0)
    accuracy_rate = accurate_predictions / len(errors) * 100
    
    print("=== 學習結果分析 ===")
    print(f"最終平均誤差: {final_avg_error:.1f}%")
    print(f"準確預測數量: {accurate_predictions}/{len(errors)}")
    print(f"準確率 (≤9%): {accuracy_rate:.1f}%")
    print(f"最大誤差: {max(errors):.1f}%")
    print(f"最小誤差: {min(errors):.1f}%")
    
    # 學習進度分析
    if len(learning_progress) >= 3:
        initial_error = learning_progress[0]['avg_error']
        final_error = learning_progress[-1]['avg_error']
        improvement = initial_error - final_error
        print(f"\n學習改善:")
        print(f"  初始誤差: {initial_error:.1f}%")
        print(f"  最終誤差: {final_error:.1f}%")
        print(f"  改善幅度: {improvement:.1f}%")
    
    # 最終係數狀態
    final_stats = formula.get_performance_stats()
    print(f"\n最終校準係數:")
    for key, value in final_stats['current_coefficients'].items():
        print(f"  {key}: {value:.3f}")
    print(f"  GL轉換係數: {final_stats['current_gl_factor']:.3f}")
    
    return final_avg_error, accuracy_rate, learning_progress

def test_final_formula_validation():
    """最終公式驗證測試"""
    print("\n=== 最終公式驗證測試 ===\n")
    
    # 使用學習後的公式進行驗證
    formula = FinalOptimizedFormula()
    
    # 先進行一輪快速學習
    validator = ClinicalDataValidator()
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0, fiber_g=3.0, protein_g=15.0, fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED
    )
    
    # 快速學習階段
    for patient_data in validator.getgoal_patients[:3]:  # 用前3個患者學習
        person = create_practical_test_person(patient_data)
        context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=4.0, stress_level=3)
        meal_history = MealHistory()
        
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        formula.learn_from_feedback(predicted_ppg, actual_ppg, person, standard_meal)
    
    # 驗證階段 - 使用剩餘患者
    validation_errors = []
    
    print("驗證階段（使用學習後的係數）:")
    for i, patient_data in enumerate(validator.getgoal_patients[3:], 4):
        person = create_practical_test_person(patient_data)
        context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=4.0, stress_level=3)
        meal_history = MealHistory()
        
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        validation_errors.append(error)
        
        print(f"患者 {i}: 實際{actual_ppg:.1f}, 預測{predicted_ppg:.1f}, 誤差{error:.1f}%")
    
    if validation_errors:
        validation_avg_error = np_mean(validation_errors)
        validation_accuracy = sum(1 for e in validation_errors if e <= 9.0) / len(validation_errors) * 100
        
        print(f"\n驗證結果:")
        print(f"  平均誤差: {validation_avg_error:.1f}%")
        print(f"  準確率: {validation_accuracy:.1f}%")
        
        return validation_avg_error, validation_accuracy
    
    return 0, 0

def test_different_meal_scenarios():
    """測試不同餐食場景"""
    print("\n=== 不同餐食場景測試 ===\n")
    
    formula = FinalOptimizedFormula()
    
    # 創建標準測試人員
    person = create_practical_test_person({
        'age': 50, 'weight_kg': 75, 'bmi': 25.0, 'hba1c': 6.5
    })
    
    # 不同餐食場景
    test_scenarios = [
        {
            'name': '早餐 - 燕麥粥',
            'meal': FoodComposition(
                carb_type=CarbType.COMPLEX_CARB, total_carbs_g=45.0,
                fiber_g=8.0, protein_g=12.0, fat_g=3.0,
                processing_level=ProcessingLevel.MINIMAL,
                cooking_method=CookingMethod.BOILED
            ),
            'context': MealContext(time_of_day=TimeOfDay.MORNING, fasting_hours=10.0, stress_level=2)
        },
        {
            'name': '午餐 - 糙米便當',
            'meal': FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=65.0,
                fiber_g=4.0, protein_g=25.0, fat_g=12.0,
                processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.STEAMED
            ),
            'context': MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=5.0, stress_level=3)
        },
        {
            'name': '晚餐 - 義大利麵',
            'meal': FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=70.0,
                fiber_g=3.0, protein_g=15.0, fat_g=8.0,
                processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED
            ),
            'context': MealContext(time_of_day=TimeOfDay.EVENING, fasting_hours=6.0, stress_level=2)
        }
    ]
    
    for scenario in test_scenarios:
        meal_history = MealHistory()
        result = formula.calculate_comprehensive_glycemic_index(
            scenario['meal'], person, scenario['context'], meal_history
        )
        
        print(f"{scenario['name']}:")
        print(f"  升糖負荷: {result['final_gl']:.1f}")
        print(f"  升糖速率: {result['glycemic_rate']:.1f}")
        print(f"  預測血糖變化: {result['predicted_ppg_change']:.1f} mg/dL")
        print(f"  風險等級: {result['risk_level']}")
        print(f"  校準因子: {result['total_calibration']:.3f}")
        print()

if __name__ == "__main__":
    print("最終優化升糖基準指數公式測試")
    print("目標：通過學習達到9%以下預測誤差")
    print("=" * 60)
    
    # 學習測試
    avg_error, accuracy_rate, progress = test_final_formula_with_learning()
    
    # 驗證測試
    val_error, val_accuracy = test_final_formula_validation()
    
    # 場景測試
    test_different_meal_scenarios()
    
    print("=== 最終評估 ===")
    if avg_error <= 9.0:
        print(f"🎉 成功達成目標！平均誤差 {avg_error:.1f}% ≤ 9%")
    elif avg_error <= 15.0:
        print(f"📈 接近目標！平均誤差 {avg_error:.1f}%，顯著改善")
    else:
        print(f"⚠️  需要進一步優化，當前誤差 {avg_error:.1f}%")
    
    print(f"準確率: {accuracy_rate:.1f}%")
    if val_error > 0:
        print(f"驗證誤差: {val_error:.1f}%")
    
    print("\n最終公式特色:")
    print("✅ 僅使用用戶可自行提供的參數")
    print("✅ 基於真實臨床數據校準")
    print("✅ 具備在線學習能力")
    print("✅ 動態係數調整機制")
    print("✅ 多層次精準校準系統")
