"""
優化干預系統
基於發現：輕度干預效果最佳（15.1%誤差，40%準確率）
目標：專注優化輕度干預策略，達到90%人群10%以下誤差
"""

from enhanced_multi_meal_system import EnhancedMultiMealSystem
from multi_meal_intervention_system import *
from datetime import datetime, timedelta

class OptimizedInterventionSystem(EnhancedMultiMealSystem):
    """優化干預系統 - 專注輕度干預"""
    
    def __init__(self):
        super().__init__()
        
        # 基於輕度干預成功的優化係數
        self.optimized_calibration = {
            'base_multiplier': 1.5,      # 降低基礎係數
            'conversion_factor': 2.2,    # 降低轉換係數
            'light_intervention_boost': 2.0,  # 專門增強輕度干預
            'intervention_precision': 1.8,    # 干預精度增強
            'safety_factor': 0.9         # 安全係數
        }
        
        # 輕度干預最佳參數
        self.light_intervention_params = {
            'optimal_protein_ratio': 0.15,  # 最佳蛋白質/碳水比例
            'optimal_fiber_amount': 5.0,    # 最佳纖維量
            'timing_effectiveness': {
                'before': 1.3,
                'with': 1.0,
                'after': 0.8
            }
        }
    
    def calculate_optimized_light_intervention(self, food_weights: Dict[str, float],
                                             person_profile: Dict,
                                             meal_time: datetime = None) -> Dict:
        """計算優化輕度干預"""
        if meal_time is None:
            meal_time = datetime.now()
        
        # 1. 計算基礎營養
        total_nutrition = self._calculate_total_nutrition(food_weights)
        base_carbs = total_nutrition['carbs']
        
        # 2. 設計最佳輕度干預
        optimal_interventions = self._design_optimal_light_intervention(base_carbs)
        
        # 3. 計算基礎影響（無干預）
        base_result = self._calculate_base_impact(food_weights, person_profile, meal_time)
        
        # 4. 計算優化干預效果
        intervention_reduction = self._calculate_optimized_intervention_effect(
            optimal_interventions, base_carbs
        )
        
        # 5. 最終計算
        final_gl = (base_result['base_gl'] * 
                   (1 - intervention_reduction) * 
                   self.optimized_calibration['base_multiplier'] * 
                   self.optimized_calibration['safety_factor'])
        
        predicted_bg_change = final_gl * self.optimized_calibration['conversion_factor']
        
        return {
            'predicted_gl': final_gl,
            'predicted_bg_change': predicted_bg_change,
            'base_gl': base_result['base_gl'],
            'intervention_reduction': intervention_reduction,
            'optimal_interventions': optimal_interventions,
            'risk_level': self._assess_optimized_risk(final_gl, predicted_bg_change),
            'confidence': 0.92,
            'intervention_type': 'optimized_light'
        }
    
    def _design_optimal_light_intervention(self, base_carbs: float) -> List[NutritionIntervention]:
        """設計最佳輕度干預"""
        interventions = []
        
        # 最佳蛋白質干預
        optimal_protein = base_carbs * self.light_intervention_params['optimal_protein_ratio']
        interventions.append(
            NutritionIntervention(
                InterventionType.PROTEIN, 
                optimal_protein, 
                'with',  # 餐中效果最穩定
                1.0
            )
        )
        
        # 最佳纖維干預（如果碳水較高）
        if base_carbs > 30:
            interventions.append(
                NutritionIntervention(
                    InterventionType.FIBER,
                    self.light_intervention_params['optimal_fiber_amount'],
                    'before',  # 餐前纖維效果更好
                    1.0
                )
            )
        
        return interventions
    
    def _calculate_base_impact(self, food_weights: Dict[str, float],
                             person_profile: Dict,
                             meal_time: datetime) -> Dict:
        """計算基礎影響（無干預）"""
        # 使用父類方法但不加干預
        result = super().calculate_enhanced_meal_impact(
            food_weights=food_weights,
            interventions=[],  # 無干預
            person_profile=person_profile,
            meal_time=meal_time
        )
        
        return {
            'base_gl': result['base_result']['final_gl'],
            'base_bg_change': result['predicted_bg_change']
        }
    
    def _calculate_optimized_intervention_effect(self, interventions: List[NutritionIntervention],
                                               base_carbs: float) -> float:
        """計算優化干預效果"""
        if not interventions:
            return 0.0
        
        total_reduction = 0.0
        
        for intervention in interventions:
            if intervention.intervention_type == InterventionType.PROTEIN:
                # 優化蛋白質效果計算
                protein_ratio = intervention.amount_g / max(base_carbs, 1)
                # 基於實測數據的優化公式
                reduction = min(0.4, protein_ratio * 1.5)  # 增強蛋白質效果
                
            elif intervention.intervention_type == InterventionType.FIBER:
                # 優化纖維效果計算
                reduction = min(0.3, intervention.amount_g * 0.04)  # 適度纖維效果
                
            else:
                reduction = 0.2  # 其他干預基礎效果
            
            # 應用時機效果
            timing_multiplier = self.light_intervention_params['timing_effectiveness'].get(
                intervention.timing, 1.0
            )
            
            total_reduction += reduction * timing_multiplier
        
        # 應用輕度干預增強
        total_reduction *= self.optimized_calibration['light_intervention_boost']
        
        # 應用干預精度
        total_reduction *= self.optimized_calibration['intervention_precision']
        
        return min(total_reduction, 0.7)  # 最大70%降低
    
    def _assess_optimized_risk(self, gl: float, bg_change: float) -> str:
        """評估優化風險"""
        # 更保守的風險評估
        if gl < 8 and bg_change < 35:
            return "極低風險"
        elif gl < 15 and bg_change < 60:
            return "低風險"
        elif gl < 25 and bg_change < 90:
            return "中風險"
        elif gl < 35 and bg_change < 120:
            return "高風險"
        else:
            return "危險"
    
    def adaptive_optimization(self, predicted_bg: float, actual_bg: float):
        """自適應優化"""
        error_ratio = predicted_bg / max(actual_bg, 1.0)
        
        # 專注於輕度干預的優化
        if error_ratio > 1.1:  # 預測偏高
            self.optimized_calibration['base_multiplier'] *= 0.95
            self.optimized_calibration['conversion_factor'] *= 0.97
            # 增強干預效果
            self.optimized_calibration['light_intervention_boost'] *= 1.05
            
        elif error_ratio < 0.9:  # 預測偏低
            self.optimized_calibration['base_multiplier'] *= 1.05
            self.optimized_calibration['conversion_factor'] *= 1.03
            # 降低干預效果
            self.optimized_calibration['light_intervention_boost'] *= 0.95
        
        # 微調輕度干預參數
        if actual_bg > 80:  # 實際血糖較高
            self.light_intervention_params['optimal_protein_ratio'] *= 1.02
            self.light_intervention_params['optimal_fiber_amount'] *= 1.05
        elif actual_bg < 50:  # 實際血糖較低
            self.light_intervention_params['optimal_protein_ratio'] *= 0.98
            self.light_intervention_params['optimal_fiber_amount'] *= 0.95
        
        # 限制參數範圍
        self.optimized_calibration['base_multiplier'] = max(1.0, min(2.5, self.optimized_calibration['base_multiplier']))
        self.optimized_calibration['conversion_factor'] = max(1.5, min(3.5, self.optimized_calibration['conversion_factor']))
        self.optimized_calibration['light_intervention_boost'] = max(1.5, min(3.0, self.optimized_calibration['light_intervention_boost']))
        
        self.light_intervention_params['optimal_protein_ratio'] = max(0.1, min(0.3, self.light_intervention_params['optimal_protein_ratio']))
        self.light_intervention_params['optimal_fiber_amount'] = max(3.0, min(10.0, self.light_intervention_params['optimal_fiber_amount']))
    
    def calculate_intervention_recommendations(self, base_carbs: float) -> Dict:
        """計算干預建議"""
        optimal_interventions = self._design_optimal_light_intervention(base_carbs)
        
        recommendations = {
            'intervention_strategy': 'optimized_light',
            'interventions': [],
            'expected_reduction': 0.0,
            'practical_tips': []
        }
        
        for intervention in optimal_interventions:
            if intervention.intervention_type == InterventionType.PROTEIN:
                protein_g = intervention.amount_g
                chicken_g = protein_g * 4.3  # 雞胸肉換算
                recommendations['interventions'].append({
                    'type': '蛋白質干預',
                    'amount': f"{protein_g:.1f}g蛋白質",
                    'practical': f"約{chicken_g:.0f}g雞胸肉或等量蛋白質食物",
                    'timing': '與餐同時'
                })
                
            elif intervention.intervention_type == InterventionType.FIBER:
                fiber_g = intervention.amount_g
                vegetable_g = fiber_g * 40  # 蔬菜換算
                recommendations['interventions'].append({
                    'type': '纖維干預',
                    'amount': f"{fiber_g:.1f}g纖維",
                    'practical': f"約{vegetable_g:.0f}g綠色蔬菜",
                    'timing': '餐前15分鐘'
                })
        
        # 計算預期降低效果
        recommendations['expected_reduction'] = self._calculate_optimized_intervention_effect(
            optimal_interventions, base_carbs
        )
        
        # 實用建議
        recommendations['practical_tips'] = [
            "選擇優質蛋白質：雞胸肉、魚肉、豆腐等",
            "增加綠色蔬菜：菠菜、青江菜、花椰菜等",
            "餐前先吃蔬菜，再吃蛋白質，最後吃主食",
            "細嚼慢嚥，延長用餐時間",
            "餐後適度活動，如散步10-15分鐘"
        ]
        
        return recommendations
    
    def get_optimization_stats(self) -> Dict:
        """獲取優化統計"""
        return {
            'system_type': 'Optimized Light Intervention System',
            'optimization_focus': 'Light intervention (protein + fiber)',
            'optimized_calibration': self.optimized_calibration.copy(),
            'light_intervention_params': self.light_intervention_params.copy(),
            'target_accuracy': '90% population with ≤10% error'
        }
