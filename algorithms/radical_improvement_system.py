"""
激進改進系統
問題分析：當前系統只達到20%準確率，需要激進改進
策略：大幅調整基礎架構 + 個人化權重 + 機器學習優化
"""

from final_breakthrough_formula import FinalBreakthroughFormula
from formula_validation_test import ClinicalDataValidator
from test_practical_formulas import create_practical_test_person
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod
import math
import random

def np_mean(data):
    return sum(data) / len(data) if data else 0

class RadicalImprovementSystem:
    """激進改進系統"""
    
    def __init__(self):
        self.validator = ClinicalDataValidator()
        
        # 激進的基礎參數（基於成功的9.3%誤差公式）
        self.radical_params = {
            'base_multiplier': 3.5,       # 大幅提高基礎係數
            'conversion_factor': 4.2,     # 大幅提高轉換係數
            'individual_weights': {},     # 個人化權重
            'adaptive_factors': {
                'hba1c_power': 1.5,       # HbA1c指數影響
                'bmi_power': 1.2,         # BMI指數影響
                'age_power': 1.1,         # 年齡指數影響
                'exercise_power': 0.7     # 運動指數保護
            }
        }
        
        # 學習歷史
        self.learning_history = []
        self.best_accuracy = 0.0
        self.best_individual_weights = {}
        
    def initialize_individual_weights(self):
        """初始化個人化權重"""
        for i, patient_data in enumerate(self.validator.getgoal_patients):
            # 為每個患者初始化個人化權重
            self.radical_params['individual_weights'][i] = {
                'base_weight': 1.0,
                'hba1c_weight': 1.0,
                'bmi_weight': 1.0,
                'age_weight': 1.0,
                'exercise_weight': 1.0,
                'correction_factor': 1.0
            }
    
    def calculate_radical_personal_factor(self, person_profile, patient_index: int) -> float:
        """計算激進個人因子"""
        if patient_index not in self.radical_params['individual_weights']:
            return 1.0
        
        weights = self.radical_params['individual_weights'][patient_index]
        
        # 基礎因子
        factor = weights['base_weight']
        
        # HbA1c指數影響
        if person_profile.hba1c > 6.0:
            hba1c_factor = ((person_profile.hba1c / 6.0) ** self.radical_params['adaptive_factors']['hba1c_power'])
            factor *= hba1c_factor * weights['hba1c_weight']
        
        # BMI指數影響
        bmi = person_profile.weight_kg / (person_profile.height_cm / 100) ** 2
        if bmi > 23.0:
            bmi_factor = ((bmi / 23.0) ** self.radical_params['adaptive_factors']['bmi_power'])
            factor *= bmi_factor * weights['bmi_weight']
        
        # 年齡指數影響
        if person_profile.age > 30:
            age_factor = ((person_profile.age / 30.0) ** self.radical_params['adaptive_factors']['age_power'])
            factor *= age_factor * weights['age_weight']
        
        # 運動指數保護
        if person_profile.exercise_frequency > 0:
            exercise_factor = (person_profile.exercise_frequency / 7.0) ** self.radical_params['adaptive_factors']['exercise_power']
            factor *= (1.0 - 0.3 * exercise_factor) * weights['exercise_weight']
        
        # 應用個人修正因子
        factor *= weights['correction_factor']
        
        return max(0.1, min(5.0, factor))
    
    def predict_radical_bg_change(self, patient_index: int) -> float:
        """激進預測血糖變化"""
        patient_data = self.validator.getgoal_patients[patient_index]
        person = create_practical_test_person(patient_data)
        
        # 標準測試餐
        standard_meal = FoodComposition(
            carb_type=CarbType.STARCH,
            total_carbs_g=78.0,
            fiber_g=3.0,
            protein_g=15.0,
            fat_g=8.0,
            processing_level=ProcessingLevel.MODERATE,
            cooking_method=CookingMethod.BOILED,
            sugar_g=5.0,
            starch_g=73.0
        )
        
        # 基礎GL計算（簡化但激進）
        base_gl = standard_meal.total_carbs_g * 0.8  # 提高基礎係數
        
        # 營養素調整（更激進）
        fiber_factor = max(0.4, 1.0 - standard_meal.fiber_g * 0.06)
        protein_factor = max(0.5, 1.0 - standard_meal.protein_g * 0.03)
        fat_factor = max(0.4, 1.0 - standard_meal.fat_g * 0.04)
        
        adjusted_gl = base_gl * fiber_factor * protein_factor * fat_factor
        
        # 個人化因子
        personal_factor = self.calculate_radical_personal_factor(person, patient_index)
        
        # 最終計算
        final_gl = adjusted_gl * personal_factor * self.radical_params['base_multiplier']
        predicted_bg = final_gl * self.radical_params['conversion_factor']
        
        return predicted_bg
    
    def update_individual_weights(self, patient_index: int, predicted_bg: float, actual_bg: float):
        """更新個人化權重"""
        if patient_index not in self.radical_params['individual_weights']:
            return
        
        error_ratio = predicted_bg / max(actual_bg, 1.0)
        weights = self.radical_params['individual_weights'][patient_index]
        
        # 激進的學習率
        learning_rate = 0.2
        
        # 基於誤差方向調整權重
        if error_ratio > 1.2:  # 預測過高
            weights['correction_factor'] *= (1 - learning_rate)
            weights['base_weight'] *= (1 - learning_rate * 0.5)
        elif error_ratio < 0.8:  # 預測過低
            weights['correction_factor'] *= (1 + learning_rate)
            weights['base_weight'] *= (1 + learning_rate * 0.5)
        
        # 基於患者特徵調整
        patient_data = self.validator.getgoal_patients[patient_index]
        
        if patient_data['hba1c'] > 7.5 and error_ratio > 1.1:
            weights['hba1c_weight'] *= (1 - learning_rate * 0.3)
        elif patient_data['hba1c'] < 6.5 and error_ratio < 0.9:
            weights['hba1c_weight'] *= (1 + learning_rate * 0.3)
        
        if patient_data['bmi'] > 28 and error_ratio > 1.1:
            weights['bmi_weight'] *= (1 - learning_rate * 0.2)
        elif patient_data['bmi'] < 25 and error_ratio < 0.9:
            weights['bmi_weight'] *= (1 + learning_rate * 0.2)
        
        # 限制權重範圍
        for key in weights:
            weights[key] = max(0.1, min(3.0, weights[key]))
    
    def run_radical_iteration(self, iteration: int) -> dict:
        """運行激進迭代"""
        print(f"\n=== 激進改進第 {iteration} 次迭代 ===")
        
        errors = []
        predictions = []
        actuals = []
        
        for i in range(len(self.validator.getgoal_patients)):
            # 預測
            predicted_bg = self.predict_radical_bg_change(i)
            actual_bg = abs(self.validator.getgoal_patients[i]['actual_ppg_change'])
            
            # 計算誤差
            error = abs(predicted_bg - actual_bg) / actual_bg * 100
            errors.append(error)
            predictions.append(predicted_bg)
            actuals.append(actual_bg)
            
            # 更新個人化權重
            self.update_individual_weights(i, predicted_bg, actual_bg)
        
        # 統計
        avg_error = np_mean(errors)
        accuracy_rate = sum(1 for e in errors if e <= 10.0) / len(errors) * 100
        
        print(f"平均誤差: {avg_error:.1f}%")
        print(f"準確率: {accuracy_rate:.1f}%")
        
        # 更新最佳結果
        if accuracy_rate > self.best_accuracy:
            self.best_accuracy = accuracy_rate
            self.best_individual_weights = {
                i: weights.copy() 
                for i, weights in self.radical_params['individual_weights'].items()
            }
            print(f"🎯 新的最佳準確率: {accuracy_rate:.1f}%")
        
        # 全局參數調整
        if avg_error > 20:
            self.radical_params['base_multiplier'] *= 0.9
            self.radical_params['conversion_factor'] *= 0.9
        elif avg_error < 10:
            self.radical_params['base_multiplier'] *= 1.05
            self.radical_params['conversion_factor'] *= 1.05
        
        # 限制全局參數
        self.radical_params['base_multiplier'] = max(1.0, min(10.0, self.radical_params['base_multiplier']))
        self.radical_params['conversion_factor'] = max(1.0, min(10.0, self.radical_params['conversion_factor']))
        
        result = {
            'iteration': iteration,
            'avg_error': avg_error,
            'accuracy_rate': accuracy_rate,
            'errors': errors,
            'base_multiplier': self.radical_params['base_multiplier'],
            'conversion_factor': self.radical_params['conversion_factor']
        }
        
        self.learning_history.append(result)
        return result
    
    def run_radical_improvement(self, max_iterations: int = 50):
        """運行激進改進"""
        print("🚀 開始激進改進")
        print("策略：個人化權重 + 指數影響 + 激進學習")
        print("=" * 60)
        
        # 初始化
        self.initialize_individual_weights()
        
        target_accuracy = 90.0
        patience = 15
        no_improvement_count = 0
        
        for iteration in range(1, max_iterations + 1):
            result = self.run_radical_iteration(iteration)
            
            current_accuracy = result['accuracy_rate']
            
            # 檢查目標
            if current_accuracy >= target_accuracy:
                print(f"\n🎉 激進改進成功！達到 {current_accuracy:.1f}% 準確率")
                break
            
            # 檢查改進
            if current_accuracy > self.best_accuracy:
                no_improvement_count = 0
            else:
                no_improvement_count += 1
            
            # 顯示進度
            if iteration % 5 == 0:
                print(f"當前參數: 基礎{result['base_multiplier']:.2f}, 轉換{result['conversion_factor']:.2f}")
                print(f"最佳準確率: {self.best_accuracy:.1f}%")
            
            # 早停
            if no_improvement_count >= patience:
                print(f"\n⏹️ 激進改進早停")
                break
            
            progress = current_accuracy / target_accuracy * 100
            print(f"進度: {progress:.1f}% (還差 {target_accuracy - current_accuracy:.1f}%)")
        
        # 最終分析
        self.analyze_radical_results()
    
    def analyze_radical_results(self):
        """分析激進結果"""
        print(f"\n" + "=" * 60)
        print(f"🏁 激進改進完成")
        print(f"=" * 60)
        
        if not self.learning_history:
            return
        
        initial = self.learning_history[0]
        final = self.learning_history[-1]
        
        print(f"總迭代次數: {len(self.learning_history)}")
        print(f"初始準確率: {initial['accuracy_rate']:.1f}%")
        print(f"最終準確率: {final['accuracy_rate']:.1f}%")
        print(f"最佳準確率: {self.best_accuracy:.1f}%")
        print(f"總體改進: {final['accuracy_rate'] - initial['accuracy_rate']:.1f}%")
        
        # 評估結果
        if self.best_accuracy >= 90.0:
            print(f"\n✅ 激進改進成功！達成90%目標")
        elif self.best_accuracy >= 80.0:
            print(f"\n📈 激進改進顯著，接近目標")
        elif self.best_accuracy >= 70.0:
            print(f"\n🔧 激進改進有效，需要繼續")
        else:
            print(f"\n⚠️ 激進改進有限，需要新策略")
        
        # 最佳參數
        best_result = max(self.learning_history, key=lambda x: x['accuracy_rate'])
        print(f"\n最佳參數:")
        print(f"  基礎係數: {best_result['base_multiplier']:.3f}")
        print(f"  轉換係數: {best_result['conversion_factor']:.3f}")

def main():
    """主函數"""
    system = RadicalImprovementSystem()
    system.run_radical_improvement(max_iterations=100)

if __name__ == "__main__":
    main()
