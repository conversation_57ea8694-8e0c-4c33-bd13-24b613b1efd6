"""
平衡精準公式測試 - 目標10%以下誤差
通過智能校準找到最佳平衡點
"""

from balanced_precise_formula import BalancedPreciseFormula
from test_practical_formulas import create_practical_test_person
from formula_validation_test import ClinicalDataValidator
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

def np_mean(data):
    return sum(data) / len(data) if data else 0

def test_balanced_precise_formula():
    """測試平衡精準公式"""
    print("=== 平衡精準升糖基準指數公式測試 ===\n")
    print("目標：通過智能校準達到10%以下誤差")
    print("策略：平衡預測 + 智能學習\n")
    
    formula = BalancedPreciseFormula()
    validator = ClinicalDataValidator()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0
    )
    
    meal_history = MealHistory()
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    errors = []
    learning_progress = []
    
    print("開始智能學習過程...")
    print("-" * 60)
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"學習步驟 {i+1}: 患者年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建個人資料
        person = create_practical_test_person(patient_data)
        
        # 預測
        result = formula.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        errors.append(error)
        
        print(f"  實際: {actual_ppg:.1f} mg/dL")
        print(f"  預測: {predicted_ppg:.1f} mg/dL")
        print(f"  誤差: {error:.1f}%")
        print(f"  平衡GL: {result['balanced_base_gl']:.1f}")
        print(f"  個人因子: {result['personal_factor']:.2f}")
        print(f"  生活因子: {result['lifestyle_factor']:.2f}")
        print(f"  基礎係數: {result['base_multiplier']:.2f}")
        
        # 智能校準
        formula.smart_calibrate(predicted_ppg, actual_ppg)
        
        # 記錄學習進度
        status = formula.get_calibration_status()
        learning_progress.append({
            'step': i+1,
            'error': error,
            'base_multiplier': status['balanced_calibration']['base_multiplier'],
            'conversion_factor': status['balanced_conversion']
        })
        
        # 檢查是否達到目標
        if formula.is_in_target_range(predicted_ppg, actual_ppg):
            print(f"  ✅ 達到目標範圍！")
        else:
            print(f"  📈 繼續學習...")
        
        print()
    
    # 分析結果
    avg_error = np_mean(errors)
    accurate_predictions = sum(1 for e in errors if e <= 10.0)
    accuracy_rate = accurate_predictions / len(errors) * 100
    
    print("=== 學習結果分析 ===")
    print(f"最終平均誤差: {avg_error:.1f}%")
    print(f"準確預測數量: {accurate_predictions}/{len(errors)}")
    print(f"準確率 (≤10%): {accuracy_rate:.1f}%")
    print(f"最大誤差: {max(errors):.1f}%")
    print(f"最小誤差: {min(errors):.1f}%")
    
    # 學習進度分析
    if len(learning_progress) >= 3:
        initial_error = errors[0]
        final_error = errors[-1]
        improvement = initial_error - final_error
        print(f"\n學習改善:")
        print(f"  初始誤差: {initial_error:.1f}%")
        print(f"  最終誤差: {final_error:.1f}%")
        print(f"  改善幅度: {improvement:.1f}%")
    
    # 最終校準狀態
    final_status = formula.get_calibration_status()
    print(f"\n最終校準係數:")
    for key, value in final_status['balanced_calibration'].items():
        print(f"  {key}: {value:.3f}")
    print(f"  轉換係數: {final_status['balanced_conversion']:.3f}")
    
    return avg_error, accuracy_rate, learning_progress

def iterative_balanced_improvement():
    """迭代平衡改進"""
    print("\n=== 迭代平衡改進過程 ===\n")
    
    iteration = 1
    target_error = 10.0
    best_error = float('inf')
    best_formula = None
    
    while iteration <= 5:  # 最多5次迭代
        print(f"第 {iteration} 次迭代:")
        
        avg_error, accuracy_rate, progress = test_balanced_precise_formula()
        
        if avg_error < best_error:
            best_error = avg_error
            # 這裡可以保存最佳公式狀態
        
        if avg_error <= target_error:
            print(f"\n🎉 成功！第 {iteration} 次迭代達到目標")
            print(f"最終平均誤差: {avg_error:.1f}% ≤ {target_error}%")
            print(f"準確率: {accuracy_rate:.1f}%")
            break
        else:
            print(f"\n📈 第 {iteration} 次迭代結果: {avg_error:.1f}%")
            print(f"距離目標還有: {avg_error - target_error:.1f}%")
            print(f"當前最佳誤差: {best_error:.1f}%")
            
            if iteration < 5:
                print("繼續優化...\n")
                print("=" * 60)
        
        iteration += 1
    
    if iteration > 5:
        print(f"\n📊 經過5次迭代，最佳誤差: {best_error:.1f}%")
        if best_error <= 15.0:
            print("✅ 顯著改善，接近目標")
        else:
            print("⚠️  需要進一步的算法改進")

def validate_balanced_formula():
    """驗證平衡公式的穩定性"""
    print("\n=== 平衡公式穩定性驗證 ===\n")
    
    # 使用學習後的公式進行驗證
    formula = BalancedPreciseFormula()
    validator = ClinicalDataValidator()
    
    # 快速學習階段（使用前3個患者）
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0, fiber_g=3.0, protein_g=15.0, fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED
    )
    
    context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=4.0, stress_level=3)
    meal_history = MealHistory()
    
    # 學習階段
    for patient_data in validator.getgoal_patients[:3]:
        person = create_practical_test_person(patient_data)
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        formula.smart_calibrate(predicted_ppg, actual_ppg)
    
    # 驗證階段
    validation_errors = []
    print("驗證階段（使用學習後的係數）:")
    
    for i, patient_data in enumerate(validator.getgoal_patients[3:], 4):
        person = create_practical_test_person(patient_data)
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        validation_errors.append(error)
        
        print(f"患者 {i}: 實際{actual_ppg:.1f}, 預測{predicted_ppg:.1f}, 誤差{error:.1f}%")
    
    if validation_errors:
        validation_avg_error = np_mean(validation_errors)
        validation_accuracy = sum(1 for e in validation_errors if e <= 10.0) / len(validation_errors) * 100
        
        print(f"\n驗證結果:")
        print(f"  平均誤差: {validation_avg_error:.1f}%")
        print(f"  準確率: {validation_accuracy:.1f}%")
        
        return validation_avg_error, validation_accuracy
    
    return 0, 0

if __name__ == "__main__":
    print("平衡精準升糖基準指數公式測試")
    print("目標：通過智能校準達到10%以下誤差")
    print("=" * 60)
    
    # 執行迭代改進
    iterative_balanced_improvement()
    
    # 穩定性驗證
    val_error, val_accuracy = validate_balanced_formula()
    
    print("\n=== 最終評估 ===")
    print("✅ 使用平衡的預測策略")
    print("✅ 實施智能校準機制")
    print("✅ 基於誤差方向動態調整")
    print("✅ 多輪迭代尋找最佳平衡點")
    print("✅ 穩定性驗證確保可靠性")
    
    if val_error > 0:
        print(f"📊 驗證誤差: {val_error:.1f}%")
        if val_error <= 10.0:
            print("🎉 驗證階段也達到目標！")
        elif val_error <= 15.0:
            print("📈 驗證階段接近目標")
        else:
            print("⚠️  驗證階段需要進一步優化")
