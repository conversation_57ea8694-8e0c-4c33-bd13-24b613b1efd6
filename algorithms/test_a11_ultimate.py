"""
A11終極優化公式測試
目標：驗證是否達到9%以下的預測誤差
"""

from formula_a11_ultimate import FormulaA11Ultimate
from formula_comparison_test import create_extended_test_person, np_mean
from formula_validation_test import ClinicalDataValidator
from taiwan_food_simulation import TaiwanFoodDatabase
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

def test_a11_with_clinical_data():
    """使用臨床數據測試A11公式"""
    print("=== A11終極優化公式臨床驗證 ===\n")
    
    # 初始化A11公式
    formula_a11 = FormulaA11Ultimate()
    validator = ClinicalDataValidator()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0,
        water_content_pct=65.0,
        viscosity=1.5
    )
    
    meal_history = MealHistory()
    errors = []
    
    print("使用GetGoal研究數據驗證...")
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"患者 {i+1}: 年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建擴展個人資料
        person = create_extended_test_person()
        person.age = int(patient_data['age'])
        person.weight_kg = patient_data['weight_kg']
        person.hba1c = patient_data['hba1c']
        person.fasting_glucose = patient_data['baseline_fpg']
        person.diabetes_type = 2
        
        # 根據患者特徵調整擴展參數
        if patient_data['hba1c'] > 8.0:
            person.microbiome_diversity = 0.6
            person.insulin_secretion_rate = 0.7
            person.hepatic_glycogen_pct = 50.0
        elif patient_data['hba1c'] < 7.0:
            person.microbiome_diversity = 0.9
            person.insulin_secretion_rate = 1.2
            person.hepatic_glycogen_pct = 80.0
        
        if patient_data['bmi'] > 30:
            person.beneficial_bacteria_ratio = 0.6
            person.endothelial_function = 0.8
            person.crp_level = 1.5
        
        context = MealContext(
            time_of_day=TimeOfDay.MIDDAY,
            fasting_hours=4.0,
            stress_level=3
        )
        
        # A11公式預測
        result = formula_a11.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg_change = result['final_gl'] * 3.2  # 調整轉換係數
        actual_ppg_change = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg_change - actual_ppg_change) / actual_ppg_change * 100
        errors.append(error)
        
        print(f"  實際PPG變化: {actual_ppg_change:.1f} mg/dL")
        print(f"  A11預測變化: {predicted_ppg_change:.1f} mg/dL")
        print(f"  預測誤差: {error:.1f}%")
        print(f"  置信度因子: {result['confidence_factor']:.3f}")
        print(f"  情境調整: {result['context_adjustment']:.3f}")
        
        # 模擬在線學習
        formula_a11.update_with_actual_result(result['final_gl'], actual_ppg_change / 3.2)
        
        print()
    
    avg_error = np_mean(errors)
    accurate_predictions = sum(1 for e in errors if e <= 9.0)
    accuracy_rate = accurate_predictions / len(errors) * 100
    
    print(f"A11公式驗證結果:")
    print(f"  平均預測誤差: {avg_error:.1f}%")
    print(f"  準確預測數量: {accurate_predictions}/{len(errors)}")
    print(f"  準確率 (≤9%): {accuracy_rate:.1f}%")
    print(f"  最大誤差: {max(errors):.1f}%")
    print(f"  最小誤差: {min(errors):.1f}%")
    
    # 獲取模型性能統計
    performance = formula_a11.get_model_performance()
    print(f"\n模型學習統計:")
    print(f"  學習樣本數: {performance['total_predictions']}")
    print(f"  平均學習誤差: {performance['avg_error']:.1%}")
    print(f"  學習準確率: {performance['accuracy_rate']:.1%}")
    
    return avg_error, accuracy_rate

def test_a11_with_taiwan_foods():
    """使用台灣美食測試A11公式"""
    print("\n=== A11公式台灣美食測試 ===\n")
    
    formula_a11 = FormulaA11Ultimate()
    food_db = TaiwanFoodDatabase()
    person = create_extended_test_person()
    
    # 測試不同類型的台灣美食
    test_foods = ['白米飯', '牛肉麵', '珍珠奶茶', '滷肉飯', '雞排', '芒果冰']
    
    meal_history = MealHistory()
    
    for food_name in test_foods:
        print(f"測試食物: {food_name}")
        food = food_db.foods[food_name]
        
        context = MealContext(
            time_of_day=TimeOfDay.MIDDAY,
            fasting_hours=4.0,
            stress_level=3
        )
        
        result = formula_a11.calculate_comprehensive_glycemic_index(
            food, person, context, meal_history
        )
        
        print(f"  升糖負荷: {result['final_gl']:.1f}")
        print(f"  升糖速率: {result['glycemic_rate']:.1f}")
        print(f"  風險等級: {result['risk_level']}")
        print(f"  置信度: {result['ensemble_confidence']:.1%}")
        print(f"  使用權重: Kalman={result['weights_used']['kalman']:.2f}, "
              f"微生物={result['weights_used']['microbiome']:.2f}, "
              f"代謝={result['weights_used']['metabolic']:.2f}")
        print()
        
        # 更新餐食歷史
        meal_history.add_meal(0, result['final_gl'])

def test_a11_adaptability():
    """測試A11公式的適應性"""
    print("=== A11公式適應性測試 ===\n")
    
    formula_a11 = FormulaA11Ultimate()
    
    # 測試不同類型的患者
    test_cases = [
        {
            'name': '年輕健康人',
            'age': 25, 'bmi': 22.0, 'hba1c': 5.0,
            'microbiome_diversity': 0.9, 'insulin_secretion_rate': 1.3
        },
        {
            'name': '中年糖尿病前期',
            'age': 45, 'bmi': 27.0, 'hba1c': 6.2,
            'microbiome_diversity': 0.7, 'insulin_secretion_rate': 1.0
        },
        {
            'name': '老年糖尿病患者',
            'age': 70, 'bmi': 32.0, 'hba1c': 8.5,
            'microbiome_diversity': 0.5, 'insulin_secretion_rate': 0.6
        }
    ]
    
    # 標準測試餐
    test_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=50.0,
        fiber_g=2.0,
        protein_g=10.0,
        fat_g=5.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED
    )
    
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    for case in test_cases:
        print(f"測試對象: {case['name']}")
        
        person = create_extended_test_person()
        person.age = case['age']
        person.bmi = case['bmi']
        person.hba1c = case['hba1c']
        person.microbiome_diversity = case['microbiome_diversity']
        person.insulin_secretion_rate = case['insulin_secretion_rate']
        
        meal_history = MealHistory()
        
        result = formula_a11.calculate_comprehensive_glycemic_index(
            test_meal, person, context, meal_history
        )
        
        print(f"  升糖負荷: {result['final_gl']:.1f}")
        print(f"  升糖速率: {result['glycemic_rate']:.1f}")
        print(f"  風險等級: {result['risk_level']}")
        print(f"  動態權重: {result['weights_used']}")
        print(f"  置信度因子: {result['confidence_factor']:.3f}")
        print()

if __name__ == "__main__":
    print("A11終極優化公式完整測試")
    print("目標：達到9%以下的預測誤差")
    print("=" * 60)
    
    # 臨床數據驗證
    avg_error, accuracy_rate = test_a11_with_clinical_data()
    
    # 台灣美食測試
    test_a11_with_taiwan_foods()
    
    # 適應性測試
    test_a11_adaptability()
    
    print("=== 最終評估 ===")
    if avg_error <= 9.0:
        print(f"🎉 成功達成目標！A11公式平均誤差 {avg_error:.1f}% ≤ 9%")
        print(f"🎯 準確率達到 {accuracy_rate:.1f}%")
    else:
        print(f"⚠️  接近目標，A11公式平均誤差 {avg_error:.1f}%")
        print(f"📈 準確率 {accuracy_rate:.1f}%，需要進一步微調")
    
    print("\nA11公式特色:")
    print("✅ 動態權重調整系統")
    print("✅ 異常值檢測和修正")
    print("✅ 在線學習能力")
    print("✅ 多維度個人化因子")
    print("✅ 餐食情境智能調整")
