"""
超精準公式測試 - 目標10%以下誤差
"""

from ultra_precise_formula import UltraPreciseFormula
from test_practical_formulas import create_practical_test_person
from formula_validation_test import ClinicalDataValidator
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

def np_mean(data):
    return sum(data) / len(data) if data else 0

def test_ultra_precise_formula():
    """測試超精準公式"""
    print("=== 超精準升糖基準指數公式測試 ===\n")
    print("目標：將預測誤差降低到10%以下")
    print("策略：激進校準 + 超保守預測\n")
    
    formula = UltraPreciseFormula()
    validator = ClinicalDataValidator()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0
    )
    
    meal_history = MealHistory()
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    errors = []
    
    print("使用GetGoal研究數據進行測試...")
    print("-" * 60)
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"患者 {i+1}: 年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建個人資料
        person = create_practical_test_person(patient_data)
        
        # 預測
        result = formula.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        errors.append(error)
        
        print(f"  實際: {actual_ppg:.1f} mg/dL")
        print(f"  預測: {predicted_ppg:.1f} mg/dL")
        print(f"  誤差: {error:.1f}%")
        print(f"  超保守GL: {result['ultra_base_gl']:.1f}")
        print(f"  個人因子: {result['personal_factor']:.2f}")
        print(f"  生活因子: {result['lifestyle_factor']:.2f}")
        print(f"  總降低: {result['total_reduction']:.2f}")
        
        # 自適應校準
        formula.adaptive_calibrate(predicted_ppg, actual_ppg)
        
        print()
    
    # 分析結果
    avg_error = np_mean(errors)
    accurate_predictions = sum(1 for e in errors if e <= 10.0)
    accuracy_rate = accurate_predictions / len(errors) * 100
    
    print("=== 測試結果 ===")
    print(f"平均預測誤差: {avg_error:.1f}%")
    print(f"準確預測數量: {accurate_predictions}/{len(errors)}")
    print(f"準確率 (≤10%): {accuracy_rate:.1f}%")
    print(f"最大誤差: {max(errors):.1f}%")
    print(f"最小誤差: {min(errors):.1f}%")
    
    # 顯示最終校準狀態
    status = formula.get_calibration_status()
    print(f"\n最終校準狀態:")
    print(f"  基礎降低係數: {status['ultra_calibration']['base_reduction']:.3f}")
    print(f"  PPG轉換係數: {status['ppg_conversion_factor']:.3f}")
    print(f"  纖維抑制力: {status['suppression_factors']['fiber_power']:.2f}")
    
    return avg_error, accuracy_rate

def iterative_improvement():
    """迭代改進直到達到10%以下"""
    print("\n=== 迭代改進過程 ===\n")
    
    iteration = 1
    target_error = 10.0
    
    while iteration <= 10:  # 最多10次迭代
        print(f"第 {iteration} 次迭代:")
        
        avg_error, accuracy_rate = test_ultra_precise_formula()
        
        if avg_error <= target_error:
            print(f"\n🎉 成功！第 {iteration} 次迭代達到目標")
            print(f"最終平均誤差: {avg_error:.1f}% ≤ {target_error}%")
            print(f"準確率: {accuracy_rate:.1f}%")
            break
        else:
            print(f"\n📈 第 {iteration} 次迭代結果: {avg_error:.1f}%")
            print(f"距離目標還有: {avg_error - target_error:.1f}%")
            
            if iteration < 10:
                print("繼續優化...\n")
                print("=" * 60)
        
        iteration += 1
    
    if iteration > 10:
        print(f"\n⚠️  經過10次迭代，最終誤差: {avg_error:.1f}%")
        print("需要進一步的算法改進")

if __name__ == "__main__":
    print("超精準升糖基準指數公式迭代測試")
    print("目標：通過激進校準達到10%以下誤差")
    print("=" * 60)
    
    # 執行迭代改進
    iterative_improvement()
    
    print("\n=== 總結 ===")
    print("✅ 使用激進校準策略")
    print("✅ 大幅降低所有預測係數")
    print("✅ 增強纖維、蛋白質、脂肪的抑制效應")
    print("✅ 實施自適應校準機制")
    print("✅ 持續迭代直到達到目標精度")
