"""
台灣美食升糖指數模擬測試
模擬一個人外出就餐，使用台灣常見美食進行升糖基準指數測試
包含餐食疊加效應的完整模擬
"""

from advanced_glycemic_calculator import (
    AdvancedGlycemicCalculator,
    FoodComposition, PersonalProfile, MealContext, MealHistory,
    CarbType, ProcessingLevel, CookingMethod, TimeOfDay
)
import json
from datetime import datetime, timedelta

class TaiwanFoodDatabase:
    """台灣美食數據庫"""
    
    def __init__(self):
        self.foods = {
            # 主食類
            "白米飯": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=28.0, fiber_g=0.3,
                protein_g=2.6, fat_g=0.3, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.STEAMED, sugar_g=0.0, starch_g=27.7,
                water_content_pct=68.0, particle_size=1.2, viscosity=1.0, ph_value=6.8
            ),
            
            "滷肉飯": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=45.0, fiber_g=1.2,
                protein_g=18.0, fat_g=15.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.STIR_FRIED, sugar_g=3.0, starch_g=42.0,
                saturated_fat_g=6.0, unsaturated_fat_g=9.0, sodium_mg=800,
                water_content_pct=55.0, cooking_time_min=120, oil_amount_g=8.0
            ),
            
            "牛肉麵": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=52.0, fiber_g=3.5,
                protein_g=25.0, fat_g=12.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED, sugar_g=2.0, starch_g=50.0,
                sodium_mg=1200, potassium_mg=400, water_content_pct=75.0,
                cooking_time_min=180, cooking_temp_c=95
            ),
            
            "炒飯": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=48.0, fiber_g=2.0,
                protein_g=12.0, fat_g=18.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.STIR_FRIED, sugar_g=1.0, starch_g=47.0,
                oil_amount_g=15.0, cooking_time_min=15, cooking_temp_c=200,
                water_content_pct=45.0
            ),
            
            # 麵食類
            "陽春麵": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=42.0, fiber_g=2.8,
                protein_g=8.0, fat_g=2.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED, water_content_pct=70.0
            ),
            
            "擔仔麵": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=38.0, fiber_g=2.5,
                protein_g=15.0, fat_g=8.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED, sodium_mg=900
            ),
            
            # 小吃類
            "珍珠奶茶": FoodComposition(
                carb_type=CarbType.SIMPLE_SUGAR, total_carbs_g=58.0, fiber_g=0.5,
                protein_g=3.0, fat_g=8.0, processing_level=ProcessingLevel.HIGHLY,
                cooking_method=CookingMethod.RAW, sugar_g=45.0, starch_g=13.0,
                water_content_pct=85.0, viscosity=2.5
            ),
            
            "雞排": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=25.0, fiber_g=1.0,
                protein_g=28.0, fat_g=22.0, processing_level=ProcessingLevel.HIGHLY,
                cooking_method=CookingMethod.DEEP_FRIED, oil_amount_g=20.0,
                cooking_temp_c=180, cooking_time_min=8
            ),
            
            "蔥抓餅": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=35.0, fiber_g=2.0,
                protein_g=6.0, fat_g=15.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.FRIED, oil_amount_g=12.0
            ),
            
            "臭豆腐": FoodComposition(
                carb_type=CarbType.STARCH, total_carbs_g=8.0, fiber_g=3.0,
                protein_g=15.0, fat_g=18.0, processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.DEEP_FRIED, oil_amount_g=15.0
            ),
            
            # 甜點類
            "紅豆湯": FoodComposition(
                carb_type=CarbType.COMPLEX_CARB, total_carbs_g=32.0, fiber_g=8.0,
                protein_g=6.0, fat_g=0.5, processing_level=ProcessingLevel.MINIMAL,
                cooking_method=CookingMethod.BOILED, sugar_g=15.0, starch_g=17.0
            ),
            
            "芒果冰": FoodComposition(
                carb_type=CarbType.SIMPLE_SUGAR, total_carbs_g=45.0, fiber_g=2.0,
                protein_g=2.0, fat_g=1.0, processing_level=ProcessingLevel.MINIMAL,
                cooking_method=CookingMethod.RAW, sugar_g=38.0, water_content_pct=90.0
            ),
            
            # 水果類
            "台灣芒果": FoodComposition(
                carb_type=CarbType.SIMPLE_SUGAR, total_carbs_g=15.0, fiber_g=1.6,
                protein_g=0.8, fat_g=0.4, processing_level=ProcessingLevel.RAW,
                cooking_method=CookingMethod.RAW, sugar_g=13.7, water_content_pct=83.5
            ),
            
            "釋迦": FoodComposition(
                carb_type=CarbType.SIMPLE_SUGAR, total_carbs_g=25.2, fiber_g=4.4,
                protein_g=2.1, fat_g=0.3, processing_level=ProcessingLevel.RAW,
                cooking_method=CookingMethod.RAW, sugar_g=20.4, water_content_pct=71.5
            )
        }

def create_test_person() -> PersonalProfile:
    """創建測試用的個人資料"""
    return PersonalProfile(
        age=35,
        weight_kg=70.0,
        height_cm=170.0,
        body_fat_pct=18.0,
        muscle_mass_kg=32.0,
        fasting_glucose=95.0,
        hba1c=5.4,
        insulin_sensitivity=1.0,
        exercise_frequency=3,
        sleep_hours=7.0,
        stress_level=4,
        diabetes_type=0
    )

def simulate_taiwan_food_tour():
    """模擬台灣美食之旅"""
    print("=== 台灣美食升糖指數模擬測試 ===\n")
    
    # 初始化
    calculator = AdvancedGlycemicCalculator()
    food_db = TaiwanFoodDatabase()
    person = create_test_person()
    meal_history = MealHistory()
    
    print(f"測試對象: {person.age}歲男性，BMI {person.bmi:.1f}，體脂率 {person.body_fat_pct}%")
    print(f"健康狀況: 空腹血糖 {person.fasting_glucose} mg/dL，HbA1c {person.hba1c}%\n")
    
    # 模擬一天的用餐
    scenarios = [
        {
            'time': '08:00',
            'meal': '滷肉飯',
            'context': MealContext(
                time_of_day=TimeOfDay.MORNING,
                fasting_hours=10.0,
                stress_level=3,
                hours_since_last_meal=10.0
            ),
            'description': '早餐 - 傳統滷肉飯'
        },
        {
            'time': '10:30',
            'meal': '珍珠奶茶',
            'context': MealContext(
                time_of_day=TimeOfDay.LATE_MORNING,
                fasting_hours=0.0,
                hours_since_last_meal=2.5
            ),
            'description': '上午茶 - 珍珠奶茶'
        },
        {
            'time': '12:30',
            'meal': '牛肉麵',
            'context': MealContext(
                time_of_day=TimeOfDay.MIDDAY,
                fasting_hours=0.0,
                hours_since_last_meal=2.0
            ),
            'description': '午餐 - 牛肉麵'
        },
        {
            'time': '15:00',
            'meal': '雞排',
            'context': MealContext(
                time_of_day=TimeOfDay.AFTERNOON,
                fasting_hours=0.0,
                hours_since_last_meal=2.5
            ),
            'description': '下午茶 - 雞排'
        },
        {
            'time': '18:30',
            'meal': '炒飯',
            'context': MealContext(
                time_of_day=TimeOfDay.EVENING,
                fasting_hours=0.0,
                hours_since_last_meal=3.5
            ),
            'description': '晚餐 - 炒飯'
        },
        {
            'time': '21:00',
            'meal': '芒果冰',
            'context': MealContext(
                time_of_day=TimeOfDay.NIGHT,
                fasting_hours=0.0,
                hours_since_last_meal=2.5
            ),
            'description': '宵夜 - 芒果冰'
        }
    ]
    
    cumulative_risk_score = 0
    daily_gl_total = 0
    
    for i, scenario in enumerate(scenarios):
        print(f"=== {scenario['description']} ({scenario['time']}) ===")
        
        food = food_db.foods[scenario['meal']]
        context = scenario['context']
        
        # 計算升糖指數
        result = calculator.calculate_comprehensive_glycemic_index(
            food, person, context, meal_history
        )
        
        # 計算干預策略
        intervention = calculator.calculate_intervention_strategy(
            food, person, context, meal_history, target_reduction=0.3
        )
        
        # 顯示結果
        print(f"食物: {scenario['meal']}")
        print(f"基礎GL: {result['base_gl']:.1f}")
        print(f"最終GL: {result['final_gl']:.1f}")
        print(f"升糖速率: {result['glycemic_rate']:.1f}")
        print(f"峰值時間: {result['peak_time_hours']:.1f}小時")
        print(f"安全閾值: {result['safety_threshold']:.1f}")
        print(f"風險等級: {result['risk_level']}")
        print(f"預測置信度: {result['confidence_score']:.1%}")
        
        # 累積統計
        daily_gl_total += result['final_gl']
        risk_scores = {"極低風險": 1, "低風險": 2, "中等風險": 3, "高風險": 4, "極高風險": 5, "危險": 6}
        cumulative_risk_score += risk_scores.get(result['risk_level'], 3)
        
        # 顯示干預建議
        if not result['is_safe']:
            print("\n⚠️  需要干預措施:")
            fiber_int = intervention['fiber_interventions']
            if fiber_int['soluble_fiber_g'] > 0:
                print(f"   建議可溶性纖維: {fiber_int['soluble_fiber_g']:.1f}g")
            
            protein_int = intervention['protein_interventions']
            if protein_int['whey_protein_g'] > 0:
                print(f"   建議乳清蛋白: {protein_int['whey_protein_g']:.1f}g")
            
            exercise = intervention['exercise_strategy']
            if exercise['type'] != 'none':
                print(f"   建議運動: {exercise['type']} {exercise['duration_min']:.0f}分鐘")
        
        # 更新餐食歷史
        hours_since_first_meal = i * 2.5  # 簡化時間計算
        meal_history.add_meal(0, result['final_gl'])
        
        print("-" * 50)
    
    # 每日總結
    print(f"\n=== 每日總結 ===")
    print(f"總GL攝入: {daily_gl_total:.1f}")
    print(f"平均風險分數: {cumulative_risk_score / len(scenarios):.1f}/6")
    print(f"建議每日GL上限: 100-120")
    
    if daily_gl_total > 120:
        print("⚠️  每日GL攝入過高，建議調整飲食")
    elif daily_gl_total > 100:
        print("⚡ 每日GL攝入偏高，需要注意")
    else:
        print("✅ 每日GL攝入在合理範圍內")

def test_meal_stacking_effects():
    """測試餐食疊加效應"""
    print("\n=== 餐食疊加效應測試 ===\n")
    
    calculator = AdvancedGlycemicCalculator()
    food_db = TaiwanFoodDatabase()
    person = create_test_person()
    
    # 測試場景：先吃滷肉飯，30分鐘後喝珍珠奶茶
    print("場景: 先吃滷肉飯，30分鐘後喝珍珠奶茶")
    
    # 第一餐：滷肉飯
    meal_history = MealHistory()
    context1 = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=5.0,
        hours_since_last_meal=5.0
    )
    
    result1 = calculator.calculate_comprehensive_glycemic_index(
        food_db.foods['滷肉飯'], person, context1, meal_history
    )
    
    print(f"第一餐 - 滷肉飯:")
    print(f"  升糖速率: {result1['glycemic_rate']:.1f}")
    print(f"  風險等級: {result1['risk_level']}")
    
    # 更新歷史
    meal_history.add_meal(0, result1['final_gl'])
    
    # 第二餐：珍珠奶茶（30分鐘後）
    context2 = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=0.0,
        hours_since_last_meal=0.5
    )
    
    result2 = calculator.calculate_comprehensive_glycemic_index(
        food_db.foods['珍珠奶茶'], person, context2, meal_history
    )
    
    print(f"\n第二餐 - 珍珠奶茶 (30分鐘後):")
    print(f"  升糖速率: {result2['glycemic_rate']:.1f}")
    print(f"  風險等級: {result2['risk_level']}")
    
    # 比較單獨吃珍珠奶茶的效果
    empty_history = MealHistory()
    context_alone = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=5.0,
        hours_since_last_meal=5.0
    )
    
    result_alone = calculator.calculate_comprehensive_glycemic_index(
        food_db.foods['珍珠奶茶'], person, context_alone, empty_history
    )
    
    print(f"\n比較 - 單獨喝珍珠奶茶:")
    print(f"  升糖速率: {result_alone['glycemic_rate']:.1f}")
    print(f"  風險等級: {result_alone['risk_level']}")
    
    # 分析疊加效應
    stacking_effect = (result2['glycemic_rate'] - result_alone['glycemic_rate']) / result_alone['glycemic_rate'] * 100
    print(f"\n疊加效應分析:")
    print(f"  升糖速率變化: {stacking_effect:+.1f}%")
    
    if stacking_effect > 0:
        print("  ⚠️  前餐增加了後餐的升糖風險")
    else:
        print("  ✅ 前餐降低了後餐的升糖風險 (第二餐效應)")

if __name__ == "__main__":
    # 運行完整模擬
    simulate_taiwan_food_tour()
    test_meal_stacking_effects()
    
    print("\n=== 模擬總結 ===")
    print("1. 台灣美食普遍具有較高的升糖潛力")
    print("2. 餐食疊加效應顯著影響血糖反應")
    print("3. 個人化因子和時間因子是重要變數")
    print("4. 適當的營養干預可有效控制升糖速度")
    print("5. 建議搭配運動和功能性食品來優化血糖管理")
