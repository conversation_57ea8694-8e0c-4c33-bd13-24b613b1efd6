"""
最終突破公式測試 - 目標突破10%誤差壁壘
"""

from final_breakthrough_formula import FinalBreakthroughFormula
from test_practical_formulas import create_practical_test_person
from formula_validation_test import ClinicalDataValidator
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

def np_mean(data):
    return sum(data) / len(data) if data else 0

def test_breakthrough_formula():
    """測試最終突破公式"""
    print("=== 最終突破升糖基準指數公式測試 ===\n")
    print("目標：突破10%誤差壁壘")
    print("策略：精細化調整 + 突破性優化\n")
    
    formula = FinalBreakthroughFormula()
    validator = ClinicalDataValidator()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0
    )
    
    meal_history = MealHistory()
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    errors = []
    breakthrough_progress = []
    
    print("開始突破性學習過程...")
    print("-" * 60)
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"突破步驟 {i+1}: 患者年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建個人資料
        person = create_practical_test_person(patient_data)
        
        # 預測
        result = formula.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        errors.append(error)
        
        print(f"  實際: {actual_ppg:.1f} mg/dL")
        print(f"  預測: {predicted_ppg:.1f} mg/dL")
        print(f"  誤差: {error:.1f}%")
        print(f"  基礎GL: {result['base_gl']:.1f}")
        print(f"  個人因子: {result['personal_factor']:.3f}")
        print(f"  生活因子: {result['lifestyle_factor']:.3f}")
        print(f"  基礎係數: {result['base_multiplier']:.3f}")
        
        # 精準校準
        formula.precision_calibrate(predicted_ppg, actual_ppg)
        
        # 記錄突破進度
        status = formula.get_breakthrough_status()
        breakthrough_progress.append({
            'step': i+1,
            'error': error,
            'base_multiplier': status['breakthrough_calibration']['base_multiplier'],
            'conversion_factor': status['breakthrough_conversion']
        })
        
        # 檢查是否突破10%
        if error <= 10.0:
            print(f"  🎯 突破10%壁壘！")
        elif error <= 15.0:
            print(f"  📈 接近突破...")
        else:
            print(f"  🔧 持續優化...")
        
        print()
    
    # 分析突破結果
    avg_error = np_mean(errors)
    breakthrough_count = sum(1 for e in errors if e <= 10.0)
    breakthrough_rate = breakthrough_count / len(errors) * 100
    
    print("=== 突破結果分析 ===")
    print(f"最終平均誤差: {avg_error:.1f}%")
    print(f"突破10%數量: {breakthrough_count}/{len(errors)}")
    print(f"突破率: {breakthrough_rate:.1f}%")
    print(f"最大誤差: {max(errors):.1f}%")
    print(f"最小誤差: {min(errors):.1f}%")
    
    # 突破進度分析
    if len(breakthrough_progress) >= 3:
        initial_error = errors[0]
        final_error = errors[-1]
        improvement = initial_error - final_error
        print(f"\n突破改善:")
        print(f"  初始誤差: {initial_error:.1f}%")
        print(f"  最終誤差: {final_error:.1f}%")
        print(f"  改善幅度: {improvement:.1f}%")
    
    # 最終突破狀態
    final_status = formula.get_breakthrough_status()
    print(f"\n最終突破係數:")
    for key, value in final_status['breakthrough_calibration'].items():
        print(f"  {key}: {value:.3f}")
    print(f"  轉換係數: {final_status['breakthrough_conversion']:.3f}")
    print(f"精細調整參數:")
    for key, value in final_status['fine_tuning'].items():
        print(f"  {key}: {value:.3f}")
    
    return avg_error, breakthrough_rate, breakthrough_progress

def validate_breakthrough_performance():
    """驗證突破性能"""
    print("\n=== 突破性能驗證 ===\n")
    
    # 使用學習後的公式進行驗證
    formula = FinalBreakthroughFormula()
    validator = ClinicalDataValidator()
    
    # 快速學習階段（使用前3個患者）
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0, fiber_g=3.0, protein_g=15.0, fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED
    )
    
    context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=4.0, stress_level=3)
    meal_history = MealHistory()
    
    # 學習階段
    print("快速學習階段:")
    for i, patient_data in enumerate(validator.getgoal_patients[:3], 1):
        person = create_practical_test_person(patient_data)
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        formula.precision_calibrate(predicted_ppg, actual_ppg)
        print(f"  學習患者 {i}: 實際{actual_ppg:.1f}, 預測{predicted_ppg:.1f}")
    
    # 驗證階段
    validation_errors = []
    print(f"\n驗證階段:")
    
    for i, patient_data in enumerate(validator.getgoal_patients[3:], 4):
        person = create_practical_test_person(patient_data)
        result = formula.calculate_comprehensive_glycemic_index(standard_meal, person, context, meal_history)
        predicted_ppg = result['predicted_ppg_change']
        actual_ppg = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        validation_errors.append(error)
        
        status = "🎯" if error <= 10.0 else "📈" if error <= 15.0 else "🔧"
        print(f"  患者 {i}: 實際{actual_ppg:.1f}, 預測{predicted_ppg:.1f}, 誤差{error:.1f}% {status}")
    
    if validation_errors:
        validation_avg_error = np_mean(validation_errors)
        validation_breakthrough = sum(1 for e in validation_errors if e <= 10.0) / len(validation_errors) * 100
        
        print(f"\n驗證結果:")
        print(f"  平均誤差: {validation_avg_error:.1f}%")
        print(f"  突破率: {validation_breakthrough:.1f}%")
        
        return validation_avg_error, validation_breakthrough
    
    return 0, 0

def comprehensive_breakthrough_test():
    """綜合突破測試"""
    print("\n=== 綜合突破測試 ===\n")
    
    # 執行主要測試
    avg_error, breakthrough_rate, progress = test_breakthrough_formula()
    
    # 執行驗證測試
    val_error, val_breakthrough = validate_breakthrough_performance()
    
    # 綜合評估
    print("\n=== 綜合突破評估 ===")
    
    if avg_error <= 10.0:
        print(f"🎉 主要測試突破成功！平均誤差 {avg_error:.1f}% ≤ 10%")
    elif avg_error <= 12.0:
        print(f"📈 主要測試接近突破！平均誤差 {avg_error:.1f}%")
    else:
        print(f"🔧 主要測試需要進一步優化，誤差 {avg_error:.1f}%")
    
    if val_error <= 10.0:
        print(f"🎯 驗證測試突破成功！平均誤差 {val_error:.1f}% ≤ 10%")
    elif val_error <= 12.0:
        print(f"📊 驗證測試接近突破！平均誤差 {val_error:.1f}%")
    else:
        print(f"⚠️  驗證測試需要進一步優化，誤差 {val_error:.1f}%")
    
    print(f"\n突破統計:")
    print(f"  主要測試突破率: {breakthrough_rate:.1f}%")
    print(f"  驗證測試突破率: {val_breakthrough:.1f}%")
    
    # 最終判定
    overall_success = (avg_error <= 10.0) or (val_error <= 10.0)
    near_success = (avg_error <= 12.0) and (val_error <= 12.0)
    
    if overall_success:
        print(f"\n🏆 突破成功！至少一項測試達到10%以下誤差")
    elif near_success:
        print(f"\n🥈 接近成功！兩項測試都接近10%目標")
    else:
        print(f"\n🔄 需要繼續優化")
    
    return overall_success, avg_error, val_error

if __name__ == "__main__":
    print("最終突破升糖基準指數公式測試")
    print("目標：突破10%誤差壁壘")
    print("=" * 60)
    
    # 執行綜合突破測試
    success, main_error, val_error = comprehensive_breakthrough_test()
    
    print("\n=== 最終總結 ===")
    print("✅ 精細化個人因子調整")
    print("✅ 突破性生活習慣優化")
    print("✅ 精準校準機制")
    print("✅ 多階段驗證流程")
    print("✅ 綜合性能評估")
    
    if success:
        print(f"\n🎉 任務完成！成功突破10%誤差壁壘")
    else:
        print(f"\n📈 顯著進步！主要誤差{main_error:.1f}%，驗證誤差{val_error:.1f}%")
