"""
增強多餐干預系統
基於成功的9.3%誤差公式，增加多餐和干預功能
目標：90%人群達到10%以下誤差
"""

from final_breakthrough_formula import FinalBreakthroughFormula
from multi_meal_intervention_system import *
from advanced_glycemic_calculator import FoodComposition, CarbType, ProcessingLevel, CookingMethod, TimeOfDay, MealContext, MealHistory
from practical_formula_series import PracticalPersonalProfile
from datetime import datetime, timedelta
import math

class EnhancedMultiMealSystem(FinalBreakthroughFormula):
    """增強多餐干預系統"""
    
    def __init__(self):
        super().__init__()
        self.food_db = FoodPortionDatabase()
        self.intervention_calc = InterventionCalculator()
        self.meal_history = []
        
        # 基於成功公式的優化係數
        self.enhanced_calibration = {
            'base_multiplier': 2.2,      # 保持成功的基礎係數
            'conversion_factor': 2.8,    # 保持成功的轉換係數
            'intervention_effectiveness': 1.3,  # 適度增強干預效果
            'multi_meal_factor': 0.9,    # 多餐影響係數
            'portion_accuracy': 1.05     # 份量精度調整
        }
        
        # 學習參數
        self.learning_rate = 0.03
        self.target_accuracy = 0.90
    
    def calculate_enhanced_meal_impact(self, food_weights: Dict[str, float],
                                     interventions: List[NutritionIntervention],
                                     person_profile: Dict,
                                     meal_time: datetime = None) -> Dict:
        """計算增強餐食影響"""
        if meal_time is None:
            meal_time = datetime.now()
        
        # 1. 計算食物營養成分（基於份量）
        total_nutrition = self._calculate_total_nutrition(food_weights)
        
        # 2. 創建食物組成對象（用於原有公式）
        food_composition = self._create_food_composition_from_portions(total_nutrition)
        
        # 3. 創建個人資料對象
        person = self._create_person_profile(person_profile)
        
        # 4. 創建餐食情境
        context = MealContext(
            time_of_day=self._get_time_of_day(meal_time),
            fasting_hours=4.0,
            stress_level=3
        )
        
        # 5. 使用成功的突破公式計算基礎GL
        meal_history = MealHistory()
        base_result = super().calculate_comprehensive_glycemic_index(
            food_composition, person, context, meal_history
        )
        
        # 6. 計算干預效果
        intervention_reduction = self._calculate_enhanced_intervention_effect(
            interventions, total_nutrition['carbs']
        )
        
        # 7. 計算多餐效應
        multi_meal_factor = self._calculate_multi_meal_effect(meal_time)
        
        # 8. 最終計算
        final_gl = (base_result['final_gl'] * 
                   (1 - intervention_reduction) * 
                   multi_meal_factor * 
                   self.enhanced_calibration['intervention_effectiveness'])
        
        predicted_bg_change = final_gl * self.enhanced_calibration['conversion_factor']
        
        # 9. 風險評估
        risk_level = self._assess_enhanced_risk(final_gl, predicted_bg_change)
        
        return {
            'predicted_gl': final_gl,
            'predicted_bg_change': predicted_bg_change,
            'base_gl': base_result['final_gl'],
            'intervention_reduction': intervention_reduction,
            'multi_meal_factor': multi_meal_factor,
            'risk_level': risk_level,
            'confidence': 0.90,
            'total_nutrition': total_nutrition,
            'base_result': base_result
        }
    
    def _calculate_total_nutrition(self, food_weights: Dict[str, float]) -> Dict:
        """計算總營養成分"""
        total_nutrition = {
            'carbs': 0.0, 'protein': 0.0, 'fat': 0.0, 'fiber': 0.0
        }
        
        for food_name, weight in food_weights.items():
            if food_name in self.food_db.portions:
                portions, nutrition = self.food_db.calculate_portions(food_name, weight)
                for key in total_nutrition:
                    total_nutrition[key] += nutrition[key]
        
        return total_nutrition
    
    def _create_food_composition_from_portions(self, nutrition: Dict) -> FoodComposition:
        """從營養成分創建食物組成"""
        return FoodComposition(
            carb_type=CarbType.STARCH,
            total_carbs_g=nutrition['carbs'],
            fiber_g=nutrition['fiber'],
            protein_g=nutrition['protein'],
            fat_g=nutrition['fat'],
            processing_level=ProcessingLevel.MODERATE,
            cooking_method=CookingMethod.BOILED,
            sugar_g=nutrition['carbs'] * 0.1,
            starch_g=nutrition['carbs'] * 0.9
        )
    
    def _create_person_profile(self, profile: Dict):
        """創建個人資料對象"""
        return PracticalPersonalProfile(
            age=profile.get('age', 35),
            height_cm=profile.get('height_cm', 170.0),
            weight_kg=profile.get('weight_kg', 70.0),
            gender=profile.get('gender', 'M'),
            hba1c=profile.get('hba1c', 5.4),
            blood_pressure_sys=profile.get('blood_pressure_sys', 120),
            blood_pressure_dia=profile.get('blood_pressure_dia', 80),
            resting_hr=profile.get('resting_hr', 70),
            exercise_frequency=profile.get('exercise_frequency', 3),
            sleep_hours=profile.get('sleep_hours', 7.0),
            stress_level=profile.get('stress_level', 3),
            smoking=profile.get('smoking', False),
            alcohol_frequency=profile.get('alcohol_frequency', 0),
            family_diabetes_history=profile.get('family_diabetes_history', False),
            family_heart_disease=profile.get('family_heart_disease', False),
            eating_speed=profile.get('eating_speed', 1.0),
            meal_frequency=profile.get('meal_frequency', 3),
            chewing_thoroughly=profile.get('chewing_thoroughly', True),
            water_intake_liters=profile.get('water_intake_liters', 2.0)
        )
    
    def _get_time_of_day(self, meal_time: datetime):
        """獲取用餐時段"""
        hour = meal_time.hour
        if 5 <= hour < 11:
            return TimeOfDay.MORNING
        elif 11 <= hour < 17:
            return TimeOfDay.MIDDAY
        else:
            return TimeOfDay.EVENING
    
    def _calculate_enhanced_intervention_effect(self, interventions: List[NutritionIntervention], 
                                              base_carbs: float) -> float:
        """計算增強干預效果"""
        if not interventions:
            return 0.0
        
        # 使用原有干預計算器
        base_reduction = self.intervention_calc.calculate_intervention_effect(interventions, base_carbs)
        
        # 適度增強效果
        enhanced_reduction = base_reduction * self.enhanced_calibration['intervention_effectiveness']
        
        return min(enhanced_reduction, 0.7)  # 最大70%降低
    
    def _calculate_multi_meal_effect(self, current_meal_time: datetime) -> float:
        """計算多餐效應"""
        if not self.meal_history:
            return 1.0
        
        cumulative_effect = 1.0
        
        for meal_time, meal_gl in self.meal_history:
            time_diff = (current_meal_time - meal_time).total_seconds() / 3600  # 小時
            
            if 0 < time_diff <= 6:  # 6小時內有影響
                decay_factor = math.exp(-time_diff / 4)  # 4小時半衰期
                effect = meal_gl * decay_factor * 0.05  # 5%影響
                cumulative_effect += effect
        
        return min(cumulative_effect * self.enhanced_calibration['multi_meal_factor'], 1.5)
    
    def _assess_enhanced_risk(self, gl: float, bg_change: float) -> str:
        """評估增強風險"""
        if gl < 10 and bg_change < 40:
            return "極低風險"
        elif gl < 20 and bg_change < 70:
            return "低風險"
        elif gl < 30 and bg_change < 100:
            return "中風險"
        elif gl < 40 and bg_change < 130:
            return "高風險"
        else:
            return "危險"
    
    def add_meal_to_history(self, meal_time: datetime, gl: float):
        """添加餐食到歷史"""
        self.meal_history.append((meal_time, gl))
        
        # 保持歷史在24小時內
        cutoff_time = meal_time - timedelta(hours=24)
        self.meal_history = [(t, g) for t, g in self.meal_history if t > cutoff_time]
    
    def update_with_actual_result(self, predicted_bg: float, actual_bg: float):
        """更新實際結果"""
        error_ratio = predicted_bg / max(actual_bg, 1.0)
        
        # 溫和的自適應調整
        if error_ratio > 1.1:  # 預測偏高
            self.enhanced_calibration['base_multiplier'] *= 0.98
            self.enhanced_calibration['conversion_factor'] *= 0.99
        elif error_ratio < 0.9:  # 預測偏低
            self.enhanced_calibration['base_multiplier'] *= 1.02
            self.enhanced_calibration['conversion_factor'] *= 1.01
        
        # 限制係數範圍
        self.enhanced_calibration['base_multiplier'] = max(1.5, min(3.0, self.enhanced_calibration['base_multiplier']))
        self.enhanced_calibration['conversion_factor'] = max(2.0, min(4.0, self.enhanced_calibration['conversion_factor']))
    
    def design_optimal_intervention(self, base_carbs: float, target_reduction: float = 0.3) -> List[NutritionIntervention]:
        """設計最佳干預策略"""
        interventions = []
        
        # 蛋白質干預
        protein_amount = base_carbs * 0.25
        interventions.append(
            NutritionIntervention(InterventionType.PROTEIN, protein_amount, 'with', 1.0)
        )
        
        # 纖維干預
        fiber_amount = min(12, base_carbs * 0.2)
        interventions.append(
            NutritionIntervention(InterventionType.FIBER, fiber_amount, 'before', 1.0)
        )
        
        # 如果需要更強干預
        if target_reduction > 0.4:
            # 添加不飽和脂肪
            fat_amount = min(15, base_carbs * 0.2)
            interventions.append(
                NutritionIntervention(InterventionType.UNSATURATED_FAT, fat_amount, 'with', 1.0)
            )
            
            # 添加醋干預
            interventions.append(
                NutritionIntervention(InterventionType.VINEGAR, 15, 'before', 1.0)
            )
        
        return interventions
    
    def get_performance_stats(self) -> Dict:
        """獲取性能統計"""
        return {
            'enhanced_calibration': self.enhanced_calibration.copy(),
            'meal_history_count': len(self.meal_history),
            'system_type': 'Enhanced Multi-Meal System',
            'base_formula': 'Final Breakthrough Formula (9.3% error)'
        }
