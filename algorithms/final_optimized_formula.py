"""
最終優化升糖基準指數公式
基於實用測試結果的深度校準版本
目標：將預測誤差降低到9%以下

問題分析：
- 當前A1公式平均誤差65.7%，主要是預測值偏高
- 轉換係數2.2可能過高，需要調整
- 需要更精確的校準機制
"""

from practical_formula_series import PracticalFormulaA1, PracticalPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory
from typing import Dict

class FinalOptimizedFormula(PracticalFormulaA1):
    """最終優化公式 - 基於實測數據校準"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "最終優化版"
        self.version = "Final"
        
        # 基於實測數據的校準係數
        self.calibration_coefficients = {
            'base_multiplier': 0.6,      # 大幅降低基礎係數
            'hba1c_factor': 0.8,         # HbA1c影響係數
            'bmi_factor': 0.9,           # BMI影響係數
            'age_factor': 0.95,          # 年齡影響係數
            'carb_sensitivity': 0.7      # 碳水敏感係數
        }
        
        # 血糖轉換係數（從GL到mg/dL）
        self.gl_to_mgdl_factor = 1.0  # 大幅降低轉換係數
        
        # 學習記錄
        self.prediction_errors = []
        self.calibration_history = []
    
    def calculate_precision_adjustment(self, person: PracticalPersonalProfile, 
                                     food: FoodComposition) -> float:
        """計算精準調整因子"""
        adjustment = self.calibration_coefficients['base_multiplier']
        
        # HbA1c精準調整
        if person.hba1c > 8.0:
            hba1c_adj = 1.0 + (person.hba1c - 8.0) * self.calibration_coefficients['hba1c_factor']
        elif person.hba1c > 7.0:
            hba1c_adj = 1.0 + (person.hba1c - 7.0) * 0.5 * self.calibration_coefficients['hba1c_factor']
        else:
            hba1c_adj = 1.0
        
        adjustment *= hba1c_adj
        
        # BMI精準調整
        if person.bmi > 30:
            bmi_adj = 1.0 + (person.bmi - 30) * 0.03 * self.calibration_coefficients['bmi_factor']
        elif person.bmi > 25:
            bmi_adj = 1.0 + (person.bmi - 25) * 0.02 * self.calibration_coefficients['bmi_factor']
        else:
            bmi_adj = 1.0
        
        adjustment *= bmi_adj
        
        # 年齡調整
        if person.age > 60:
            age_adj = 1.0 + (person.age - 60) * 0.01 * self.calibration_coefficients['age_factor']
        else:
            age_adj = 1.0
        
        adjustment *= age_adj
        
        # 碳水含量調整
        carb_adj = 1.0 + (food.total_carbs_g / 100.0) * self.calibration_coefficients['carb_sensitivity']
        adjustment *= carb_adj
        
        return adjustment
    
    def calculate_food_impact_reduction(self, food: FoodComposition) -> float:
        """計算食物影響降低因子"""
        reduction = 1.0
        
        # 纖維的強力降低效應
        if food.fiber_g > 5:
            reduction *= 0.7  # 高纖維大幅降低
        elif food.fiber_g > 2:
            reduction *= 0.85
        
        # 蛋白質的緩解效應
        if food.protein_g > 20:
            reduction *= 0.8
        elif food.protein_g > 10:
            reduction *= 0.9
        
        # 脂肪的延緩效應
        if food.fat_g > 15:
            reduction *= 0.75
        elif food.fat_g > 5:
            reduction *= 0.9
        
        # 加工程度的影響
        if food.processing_level.value <= 2:  # 最小加工
            reduction *= 0.8
        elif food.processing_level.value >= 4:  # 高度加工
            reduction *= 1.2
        
        return reduction
    
    def calculate_individual_sensitivity(self, person: PracticalPersonalProfile) -> float:
        """計算個體敏感性"""
        sensitivity = 1.0
        
        # 運動頻率的保護效應
        if person.exercise_frequency >= 4:
            sensitivity *= 0.7
        elif person.exercise_frequency >= 2:
            sensitivity *= 0.85
        elif person.exercise_frequency == 0:
            sensitivity *= 1.2
        
        # 睡眠質量影響
        if person.sleep_hours < 6:
            sensitivity *= 1.15
        elif person.sleep_hours > 8:
            sensitivity *= 0.95
        
        # 壓力水平影響
        if person.stress_level >= 4:
            sensitivity *= 1.1
        elif person.stress_level <= 2:
            sensitivity *= 0.95
        
        # 血壓影響
        if person.blood_pressure_sys > 140:
            sensitivity *= 1.05
        
        # 心率影響
        if person.resting_hr > 80:
            sensitivity *= 1.03
        elif person.resting_hr < 60:
            sensitivity *= 0.95
        
        return sensitivity
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: PracticalPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        base_result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # 應用精準調整
        precision_adj = self.calculate_precision_adjustment(person, food)
        food_impact_reduction = self.calculate_food_impact_reduction(food)
        individual_sensitivity = self.calculate_individual_sensitivity(person)
        
        # 計算校準後的GL
        calibrated_gl = (base_result['final_gl'] * 
                        precision_adj * 
                        food_impact_reduction * 
                        individual_sensitivity)
        
        # 更新結果
        result = base_result.copy()
        result['final_gl'] = calibrated_gl
        result['glycemic_rate'] = calibrated_gl / result['peak_time_hours']
        
        # 使用優化的轉換係數
        result['predicted_ppg_change'] = calibrated_gl * self.gl_to_mgdl_factor
        
        # 重新評估風險
        safety_threshold = self._calculate_practical_safety_threshold(person)
        result['is_safe'] = result['glycemic_rate'] <= safety_threshold
        result['risk_level'] = self._assess_practical_risk(result['glycemic_rate'], safety_threshold, person)
        
        # 添加校準信息
        result['precision_adjustment'] = precision_adj
        result['food_impact_reduction'] = food_impact_reduction
        result['individual_sensitivity'] = individual_sensitivity
        result['total_calibration'] = precision_adj * food_impact_reduction * individual_sensitivity
        
        return result
    
    def learn_from_feedback(self, predicted_ppg: float, actual_ppg: float, 
                           person: PracticalPersonalProfile, food: FoodComposition):
        """從反饋中學習並調整係數"""
        error_ratio = actual_ppg / max(predicted_ppg, 1.0)
        error_pct = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        
        self.prediction_errors.append(error_pct)
        
        # 如果預測持續偏高，降低係數
        if len(self.prediction_errors) >= 3:
            recent_errors = self.prediction_errors[-3:]
            if all(e > 50 for e in recent_errors):  # 連續3次誤差>50%
                self.calibration_coefficients['base_multiplier'] *= 0.9
                self.gl_to_mgdl_factor *= 0.9
        
        # 如果預測持續偏低，增加係數
        if len(self.prediction_errors) >= 3:
            recent_ratios = [actual_ppg / max(pred, 1.0) for pred in self.prediction_errors[-3:]]
            if all(r > 1.5 for r in recent_ratios):  # 連續3次實際值比預測高50%以上
                self.calibration_coefficients['base_multiplier'] *= 1.1
                self.gl_to_mgdl_factor *= 1.1
        
        # 基於個人特徵調整
        if error_ratio > 1.3:  # 實際值比預測高30%以上
            if person.hba1c > 8.0:
                self.calibration_coefficients['hba1c_factor'] *= 1.05
            if person.bmi > 30:
                self.calibration_coefficients['bmi_factor'] *= 1.05
        elif error_ratio < 0.7:  # 實際值比預測低30%以上
            if person.hba1c > 8.0:
                self.calibration_coefficients['hba1c_factor'] *= 0.98
            if person.bmi > 30:
                self.calibration_coefficients['bmi_factor'] *= 0.98
        
        # 限制係數範圍
        for key in self.calibration_coefficients:
            self.calibration_coefficients[key] = max(0.1, min(2.0, self.calibration_coefficients[key]))
        
        self.gl_to_mgdl_factor = max(0.1, min(5.0, self.gl_to_mgdl_factor))
        
        # 記錄校準歷史
        self.calibration_history.append({
            'error_pct': error_pct,
            'error_ratio': error_ratio,
            'coefficients': self.calibration_coefficients.copy(),
            'gl_factor': self.gl_to_mgdl_factor
        })
    
    def get_performance_stats(self) -> Dict[str, float]:
        """獲取性能統計"""
        if not self.prediction_errors:
            return {'avg_error': 0, 'improvement': 0, 'total_predictions': 0}
        
        avg_error = sum(self.prediction_errors) / len(self.prediction_errors)
        
        # 計算改善程度
        if len(self.prediction_errors) >= 5:
            early_avg = sum(self.prediction_errors[:3]) / 3
            recent_avg = sum(self.prediction_errors[-3:]) / 3
            improvement = early_avg - recent_avg
        else:
            improvement = 0
        
        return {
            'avg_error': avg_error,
            'improvement': improvement,
            'total_predictions': len(self.prediction_errors),
            'current_coefficients': self.calibration_coefficients,
            'current_gl_factor': self.gl_to_mgdl_factor
        }
