"""
超精準升糖基準指數公式 - 激進校準版
目標：將預測誤差降低到10%以下

問題分析：
- 當前誤差65.7%，預測值嚴重偏高
- 需要激進降低所有係數
- 重新設計基礎計算邏輯
"""

from practical_formula_series import PracticalFormulaA1, PracticalPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, CarbType
from typing import Dict

class UltraPreciseFormula(PracticalFormulaA1):
    """超精準公式 - 激進校準版本"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "超精準版"
        self.version = "Ultra"
        
        # 激進降低的校準係數
        self.ultra_calibration = {
            'base_reduction': 0.15,      # 基礎大幅降低85%
            'carb_sensitivity': 0.3,     # 碳水敏感性降低70%
            'hba1c_impact': 0.5,         # HbA1c影響降低50%
            'bmi_impact': 0.4,           # BMI影響降低60%
            'age_impact': 0.8,           # 年齡影響降低20%
        }
        
        # 超保守的轉換係數
        self.ppg_conversion_factor = 0.8  # 從2.2降到0.8
        
        # 強力抑制因子
        self.suppression_factors = {
            'fiber_power': 2.0,          # 纖維抑制力加倍
            'protein_power': 1.5,        # 蛋白質抑制力增強
            'fat_power': 1.8,            # 脂肪抑制力增強
            'processing_penalty': 0.7    # 加工食品懲罰
        }
    
    def calculate_ultra_base_gl(self, food: FoodComposition) -> float:
        """計算超保守的基礎GL"""
        # 使用更保守的基礎GI值
        conservative_gi = {
            CarbType.SIMPLE_SUGAR: 45,      # 從70降到45
            CarbType.DISACCHARIDE: 40,      # 從65降到40
            CarbType.STARCH: 35,            # 從55降到35
            CarbType.COMPLEX_CARB: 25,      # 從45降到25
            CarbType.RESISTANT_STARCH: 15   # 從30降到15
        }
        
        base_gi = conservative_gi.get(food.carb_type, 30)
        
        # 強力纖維抑制
        fiber_reduction = 1.0 - (food.fiber_g * 0.08 * self.suppression_factors['fiber_power'])
        fiber_reduction = max(0.2, fiber_reduction)  # 最多降低80%
        
        # 強力蛋白質抑制
        protein_reduction = 1.0 - (food.protein_g * 0.03 * self.suppression_factors['protein_power'])
        protein_reduction = max(0.3, protein_reduction)  # 最多降低70%
        
        # 強力脂肪抑制
        fat_reduction = 1.0 - (food.fat_g * 0.04 * self.suppression_factors['fat_power'])
        fat_reduction = max(0.2, fat_reduction)  # 最多降低80%
        
        # 加工程度懲罰
        if food.processing_level.value >= 3:
            processing_factor = self.suppression_factors['processing_penalty']
        else:
            processing_factor = 1.0
        
        # 計算超保守GL
        adjusted_gi = base_gi * fiber_reduction * protein_reduction * fat_reduction * processing_factor
        ultra_gl = (adjusted_gi * food.total_carbs_g * self.ultra_calibration['carb_sensitivity']) / 100
        
        return max(1.0, ultra_gl)  # 最小值1.0
    
    def calculate_ultra_personal_factor(self, person: PracticalPersonalProfile) -> float:
        """計算超保守的個人因子"""
        factor = 1.0
        
        # 超保守的HbA1c影響
        if person.hba1c > 8.0:
            hba1c_factor = 1.0 + (person.hba1c - 8.0) * 0.1 * self.ultra_calibration['hba1c_impact']
        elif person.hba1c > 7.0:
            hba1c_factor = 1.0 + (person.hba1c - 7.0) * 0.05 * self.ultra_calibration['hba1c_impact']
        else:
            hba1c_factor = 1.0
        
        factor *= hba1c_factor
        
        # 超保守的BMI影響
        if person.bmi > 30:
            bmi_factor = 1.0 + (person.bmi - 30) * 0.02 * self.ultra_calibration['bmi_impact']
        elif person.bmi > 25:
            bmi_factor = 1.0 + (person.bmi - 25) * 0.01 * self.ultra_calibration['bmi_impact']
        else:
            bmi_factor = 1.0
        
        factor *= bmi_factor
        
        # 年齡影響
        if person.age > 60:
            age_factor = 1.0 + (person.age - 60) * 0.005 * self.ultra_calibration['age_impact']
        else:
            age_factor = 1.0
        
        factor *= age_factor
        
        return factor
    
    def calculate_ultra_lifestyle_factor(self, person: PracticalPersonalProfile) -> float:
        """計算超強的生活習慣保護因子"""
        factor = 1.0
        
        # 超強運動保護
        if person.exercise_frequency >= 5:
            factor *= 0.5   # 經常運動降低50%
        elif person.exercise_frequency >= 3:
            factor *= 0.7   # 適度運動降低30%
        elif person.exercise_frequency >= 1:
            factor *= 0.85  # 少量運動降低15%
        else:
            factor *= 1.3   # 不運動增加30%
        
        # 睡眠質量超強影響
        if person.sleep_hours < 6:
            factor *= 1.4   # 睡眠不足嚴重影響
        elif person.sleep_hours > 8:
            factor *= 0.8   # 充足睡眠保護
        
        # 壓力水平超強影響
        if person.stress_level >= 4:
            factor *= 1.3   # 高壓力嚴重影響
        elif person.stress_level <= 2:
            factor *= 0.8   # 低壓力保護
        
        # 血壓影響
        if person.blood_pressure_sys > 140:
            factor *= 1.2
        elif person.blood_pressure_sys < 120:
            factor *= 0.9
        
        # 心率影響
        if person.resting_hr > 80:
            factor *= 1.1
        elif person.resting_hr < 60:
            factor *= 0.85  # 運動員心率保護
        
        return factor
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: PracticalPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 使用超保守的基礎GL
        ultra_base_gl = self.calculate_ultra_base_gl(food)
        
        # 應用超保守的個人因子
        personal_factor = self.calculate_ultra_personal_factor(person)
        
        # 應用超強的生活習慣因子
        lifestyle_factor = self.calculate_ultra_lifestyle_factor(person)
        
        # 餐食情境的保守調整
        context_factor = 1.0
        if context.fasting_hours > 8:
            context_factor *= 1.05  # 輕微增加
        elif context.fasting_hours < 3:
            context_factor *= 0.9   # 降低
        
        # 計算最終GL（應用基礎降低係數）
        final_gl = (ultra_base_gl * 
                   personal_factor * 
                   lifestyle_factor * 
                   context_factor * 
                   self.ultra_calibration['base_reduction'])
        
        # 計算峰值時間
        peak_time = self._estimate_peak_time(food, person)
        
        # 計算升糖速率
        glycemic_rate = final_gl / peak_time
        
        # 使用超保守的轉換係數
        predicted_ppg_change = final_gl * self.ppg_conversion_factor
        
        # 安全評估
        safety_threshold = self._calculate_practical_safety_threshold(person)
        is_safe = glycemic_rate <= safety_threshold
        risk_level = self._assess_practical_risk(glycemic_rate, safety_threshold, person)
        
        return {
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'peak_time_hours': peak_time,
            'predicted_ppg_change': predicted_ppg_change,
            'is_safe': is_safe,
            'risk_level': risk_level,
            'safety_threshold': safety_threshold,
            'confidence_score': 0.9,
            'ultra_base_gl': ultra_base_gl,
            'personal_factor': personal_factor,
            'lifestyle_factor': lifestyle_factor,
            'context_factor': context_factor,
            'total_reduction': self.ultra_calibration['base_reduction']
        }
    
    def adaptive_calibrate(self, predicted_ppg: float, actual_ppg: float):
        """自適應校準"""
        error_ratio = predicted_ppg / max(actual_ppg, 1.0)
        
        # 如果預測還是偏高，繼續降低係數
        if error_ratio > 1.2:
            self.ultra_calibration['base_reduction'] *= 0.9
            self.ppg_conversion_factor *= 0.9
            
            # 增強抑制因子
            for key in self.suppression_factors:
                if key != 'processing_penalty':
                    self.suppression_factors[key] *= 1.1
        
        # 如果預測偏低太多，適度增加
        elif error_ratio < 0.7:
            self.ultra_calibration['base_reduction'] *= 1.05
            self.ppg_conversion_factor *= 1.05
        
        # 限制係數範圍
        self.ultra_calibration['base_reduction'] = max(0.05, min(0.5, self.ultra_calibration['base_reduction']))
        self.ppg_conversion_factor = max(0.2, min(2.0, self.ppg_conversion_factor))
        
        for key in self.suppression_factors:
            if key != 'processing_penalty':
                self.suppression_factors[key] = max(1.0, min(3.0, self.suppression_factors[key]))
    
    def get_calibration_status(self) -> Dict:
        """獲取校準狀態"""
        return {
            'ultra_calibration': self.ultra_calibration.copy(),
            'ppg_conversion_factor': self.ppg_conversion_factor,
            'suppression_factors': self.suppression_factors.copy()
        }
