"""
實用升糖基準指數公式測試
使用真實研究的飲食參數進行血糖結果誤差比較
僅使用用戶可自行提供的參數
"""

from practical_formula_series import *
from formula_validation_test import ClinicalDataValidator
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod

def create_practical_test_person(patient_data: dict) -> PracticalPersonalProfile:
    """基於臨床數據創建實用個人資料"""
    return PracticalPersonalProfile(
        # 基本生理參數（從研究數據推算）
        age=int(patient_data['age']),
        height_cm=170.0,  # 假設平均身高
        weight_kg=patient_data['weight_kg'],
        gender="M",  # 假設，實際應用中用戶提供
        
        # 健康指標
        hba1c=patient_data['hba1c'],
        blood_pressure_sys=int(120 + (patient_data['hba1c'] - 5.4) * 10),  # 基於HbA1c估算
        blood_pressure_dia=int(80 + (patient_data['hba1c'] - 5.4) * 5),
        resting_hr=int(70 + (patient_data['bmi'] - 25) * 2),  # 基於BMI估算
        
        # 生活習慣（基於典型糖尿病患者估算）
        exercise_frequency=2 if patient_data['hba1c'] > 7.0 else 3,
        sleep_hours=6.5 if patient_data['hba1c'] > 8.0 else 7.0,
        stress_level=4 if patient_data['hba1c'] > 8.0 else 3,
        smoking=False,  # 假設不吸煙
        alcohol_frequency=1,
        
        # 家族史（基於統計估算）
        family_diabetes_history=True if patient_data['hba1c'] > 7.5 else False,
        family_heart_disease=False,
        
        # 用餐習慣（基於BMI估算）
        eating_speed=1.2 if patient_data['bmi'] > 30 else 1.0,
        meal_frequency=3,
        chewing_thoroughly=False if patient_data['bmi'] > 30 else True,
        water_intake_liters=1.8 if patient_data['bmi'] > 30 else 2.2,
        
        # 當前狀態（假設中等水平）
        current_stress=3,
        fatigue_level=2,
        sleep_quality_last_night=3
    )

def test_practical_formulas_with_clinical_data():
    """使用臨床數據測試實用公式"""
    print("=== 實用升糖基準指數公式臨床驗證 ===\n")
    print("僅使用用戶可自行提供的參數")
    print("與真實研究飲食數據進行比較\n")
    
    # 初始化所有實用公式
    formulas = {
        'A1': PracticalFormulaA1(),
        'A2': PracticalFormulaA2(),
        'A3': PracticalFormulaA3(),
        'A4': PracticalFormulaA4(),
        'A5': PracticalFormulaA5()
    }
    
    validator = ClinicalDataValidator()
    
    # 標準測試餐（基於GetGoal研究）
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0,
        water_content_pct=65.0,
        viscosity=1.5
    )
    
    meal_history = MealHistory()
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    # 存儲所有公式的結果
    all_results = {formula_name: [] for formula_name in formulas.keys()}
    
    print("使用GetGoal研究數據進行驗證...")
    print(f"測試患者數量: {len(validator.getgoal_patients)}")
    print("-" * 60)
    
    for i, patient_data in enumerate(validator.getgoal_patients):
        print(f"患者 {i+1}: 年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建實用個人資料
        person = create_practical_test_person(patient_data)
        
        # 實際血糖變化
        actual_ppg_change = abs(patient_data['actual_ppg_change'])
        
        print(f"  實際PPG變化: {actual_ppg_change:.1f} mg/dL")
        
        # 測試所有公式
        for formula_name, formula in formulas.items():
            result = formula.calculate_comprehensive_glycemic_index(
                standard_meal, person, context, meal_history
            )
            
            # 轉換為血糖變化（mg/dL）
            predicted_ppg_change = result['final_gl'] * 2.2  # 校準轉換係數
            
            # 計算誤差
            error = abs(predicted_ppg_change - actual_ppg_change) / actual_ppg_change * 100
            
            all_results[formula_name].append({
                'predicted': predicted_ppg_change,
                'actual': actual_ppg_change,
                'error': error,
                'patient_id': i+1
            })
            
            print(f"  {formula_name}: 預測{predicted_ppg_change:.1f} mg/dL, 誤差{error:.1f}%")
        
        print()
    
    return all_results

def analyze_practical_formula_performance(all_results):
    """分析實用公式性能"""
    print("=== 實用公式性能分析 ===\n")
    
    performance_stats = {}
    
    for formula_name, results in all_results.items():
        errors = [r['error'] for r in results]
        predictions = [r['predicted'] for r in results]
        actuals = [r['actual'] for r in results]
        
        # 計算統計指標
        avg_error = sum(errors) / len(errors)
        max_error = max(errors)
        min_error = min(errors)
        
        # 計算標準差
        error_variance = sum((e - avg_error) ** 2 for e in errors) / len(errors)
        error_std = error_variance ** 0.5
        
        # 準確預測率（誤差≤9%）
        accurate_count = sum(1 for e in errors if e <= 9.0)
        accuracy_rate = accurate_count / len(errors) * 100
        
        # 預測偏差
        avg_predicted = sum(predictions) / len(predictions)
        avg_actual = sum(actuals) / len(actuals)
        bias = (avg_predicted - avg_actual) / avg_actual * 100
        
        performance_stats[formula_name] = {
            'avg_error': avg_error,
            'error_std': error_std,
            'max_error': max_error,
            'min_error': min_error,
            'accuracy_rate': accuracy_rate,
            'bias': bias,
            'accurate_count': accurate_count,
            'total_count': len(errors)
        }
    
    # 按準確率排序
    sorted_formulas = sorted(performance_stats.items(), 
                           key=lambda x: x[1]['accuracy_rate'], reverse=True)
    
    print(f"{'公式':<4} {'平均誤差':<8} {'標準差':<8} {'準確率':<8} {'偏差':<8} {'最大誤差':<8}")
    print("-" * 55)
    
    for formula_name, stats in sorted_formulas:
        print(f"{formula_name:<4} {stats['avg_error']:<8.1f} {stats['error_std']:<8.1f} "
              f"{stats['accuracy_rate']:<8.1f} {stats['bias']:<8.1f} {stats['max_error']:<8.1f}")
    
    # 找出最佳公式
    best_formula = sorted_formulas[0]
    print(f"\n🏆 最佳公式: {best_formula[0]}")
    print(f"   平均誤差: {best_formula[1]['avg_error']:.1f}%")
    print(f"   準確率: {best_formula[1]['accuracy_rate']:.1f}%")
    print(f"   準確預測: {best_formula[1]['accurate_count']}/{best_formula[1]['total_count']}")
    
    # 檢查是否達到目標
    if best_formula[1]['avg_error'] <= 9.0:
        print(f"🎉 達成目標！最佳公式誤差 ≤ 9%")
    else:
        print(f"📈 接近目標，最佳公式誤差 {best_formula[1]['avg_error']:.1f}%")
    
    return performance_stats

def test_practical_formula_with_different_meals():
    """測試實用公式在不同餐食下的表現"""
    print("\n=== 不同餐食類型測試 ===\n")
    
    # 使用最佳公式（假設是A1）
    formula = PracticalFormulaA1()
    
    # 創建標準測試人員
    person = PracticalPersonalProfile(
        age=45, height_cm=170, weight_kg=75, gender="M",
        hba1c=6.5, blood_pressure_sys=130, blood_pressure_dia=85,
        exercise_frequency=3, sleep_hours=7.0
    )
    
    # 不同類型的測試餐
    test_meals = [
        {
            'name': '高糖飲料',
            'meal': FoodComposition(
                carb_type=CarbType.SIMPLE_SUGAR,
                total_carbs_g=40.0, fiber_g=0, protein_g=0, fat_g=0,
                processing_level=ProcessingLevel.HIGHLY,
                cooking_method=CookingMethod.RAW,
                sugar_g=40.0, starch_g=0
            )
        },
        {
            'name': '白米飯',
            'meal': FoodComposition(
                carb_type=CarbType.STARCH,
                total_carbs_g=60.0, fiber_g=1.0, protein_g=8.0, fat_g=1.0,
                processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED,
                sugar_g=0, starch_g=60.0
            )
        },
        {
            'name': '全麥麵包',
            'meal': FoodComposition(
                carb_type=CarbType.STARCH,
                total_carbs_g=50.0, fiber_g=8.0, protein_g=12.0, fat_g=3.0,
                processing_level=ProcessingLevel.MINIMAL,
                cooking_method=CookingMethod.BAKED,
                sugar_g=2.0, starch_g=48.0
            )
        },
        {
            'name': '高脂肪餐',
            'meal': FoodComposition(
                carb_type=CarbType.STARCH,
                total_carbs_g=30.0, fiber_g=2.0, protein_g=25.0, fat_g=40.0,
                processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.FRIED,
                sugar_g=1.0, starch_g=29.0
            )
        }
    ]
    
    context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=4.0, stress_level=3)
    meal_history = MealHistory()
    
    for meal_info in test_meals:
        result = formula.calculate_comprehensive_glycemic_index(
            meal_info['meal'], person, context, meal_history
        )
        
        print(f"{meal_info['name']}:")
        print(f"  升糖負荷: {result['final_gl']:.1f}")
        print(f"  升糖速率: {result['glycemic_rate']:.1f}")
        print(f"  風險等級: {result['risk_level']}")
        print(f"  預測血糖變化: {result['final_gl'] * 2.2:.1f} mg/dL")
        print()

if __name__ == "__main__":
    print("實用升糖基準指數公式完整測試")
    print("僅使用用戶可自行提供的參數")
    print("=" * 60)
    
    # 臨床數據驗證
    all_results = test_practical_formulas_with_clinical_data()
    
    # 性能分析
    performance_stats = analyze_practical_formula_performance(all_results)
    
    # 不同餐食測試
    test_practical_formula_with_different_meals()
    
    print("=== 總結 ===")
    print("✅ 僅使用用戶可自行提供的基本參數")
    print("✅ 無需專業醫療設備或複雜檢測")
    print("✅ 基於真實臨床研究數據驗證")
    print("✅ 提供多個優化版本供選擇")
    print("✅ 實用性和準確性並重的設計")
