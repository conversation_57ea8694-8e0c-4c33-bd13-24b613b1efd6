"""
A1-A10公式系列比較測試
目標：找出誤差最低的公式，達到9%以下的預測精度
"""

# 使用純Python實現統計函數
def np_mean(data):
    return sum(data) / len(data) if data else 0

def np_std(data):
    if not data:
        return 0
    mean = np_mean(data)
    variance = sum((x - mean) ** 2 for x in data) / len(data)
    return variance ** 0.5
from formula_series_a1_a10 import *
from taiwan_food_simulation import TaiwanFoodDatabase, create_test_person
from formula_validation_test import ClinicalDataValidator

def create_extended_test_person() -> ExtendedPersonalProfile:
    """創建擴展測試用戶"""
    base_person = create_test_person()
    
    return ExtendedPersonalProfile(
        # 基礎參數
        age=base_person.age,
        weight_kg=base_person.weight_kg,
        height_cm=base_person.height_cm,
        body_fat_pct=base_person.body_fat_pct,
        muscle_mass_kg=base_person.muscle_mass_kg,
        fasting_glucose=base_person.fasting_glucose,
        hba1c=base_person.hba1c,
        insulin_sensitivity=base_person.insulin_sensitivity,
        exercise_frequency=base_person.exercise_frequency,
        sleep_hours=base_person.sleep_hours,
        stress_level=base_person.stress_level,
        diabetes_type=base_person.diabetes_type,
        
        # 新增擴展參數
        gut_transit_time_hours=22.0,
        gastric_ph=1.8,
        bile_acid_level=1.1,
        insulin_secretion_rate=1.2,
        hepatic_glycogen_pct=75.0,
        muscle_glycogen_pct=85.0,
        glucagon_sensitivity=0.9,
        microbiome_diversity=0.85,
        beneficial_bacteria_ratio=0.75,
        scfa_production=1.1,
        endothelial_function=1.0,
        autonomic_balance=1.0,
        sympathetic_activity=0.9,
        crp_level=0.8,
        il6_level=0.9,
        tnf_alpha=0.8,
        genetic_risk_score=1.0,
        epigenetic_age=35.0,
        meal_frequency=3,
        eating_speed=1.0,
        chewing_efficiency=1.0,
        hydration_level=1.0
    )

def test_all_formulas():
    """測試所有A1-A10公式"""
    print("=== A1-A10公式系列比較測試 ===\n")
    
    # 初始化所有公式
    formulas = {
        'A1': FormulaA1(),
        'A2': FormulaA2(),
        'A3': FormulaA3(),
        'A4': FormulaA4(),
        'A5': FormulaA5(),
        'A6': FormulaA6(),
        'A7': FormulaA7(),
        'A8': FormulaA8(),
        'A9': FormulaA9(),
        'A10': FormulaA10()
    }
    
    # 測試數據
    food_db = TaiwanFoodDatabase()
    person = create_extended_test_person()
    meal_history = MealHistory()
    
    # 測試食物
    test_foods = ['白米飯', '牛肉麵', '珍珠奶茶', '雞排', '芒果冰']
    
    # 測試情境
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0,
        stress_level=3
    )
    
    results = {}
    
    print(f"測試對象: {person.age}歲，BMI {person.bmi:.1f}，HbA1c {person.hba1c}%")
    print(f"擴展參數: 微生物多樣性 {person.microbiome_diversity:.2f}，胰島素分泌率 {person.insulin_secretion_rate:.1f}")
    print("-" * 80)
    
    for food_name in test_foods:
        print(f"\n=== 測試食物: {food_name} ===")
        food = food_db.foods[food_name]
        
        food_results = {}
        
        for formula_name, formula in formulas.items():
            try:
                if formula_name in ['A2', 'A7', 'A8', 'A9', 'A10']:
                    # 需要ExtendedPersonalProfile的公式
                    result = formula.calculate_comprehensive_glycemic_index(
                        food, person, context, meal_history
                    )
                else:
                    # 使用基礎PersonalProfile的公式
                    base_person = PersonalProfile(
                        age=person.age, weight_kg=person.weight_kg, height_cm=person.height_cm,
                        body_fat_pct=person.body_fat_pct, hba1c=person.hba1c,
                        diabetes_type=person.diabetes_type
                    )
                    result = formula.calculate_comprehensive_glycemic_index(
                        food, base_person, context, meal_history
                    )
                
                food_results[formula_name] = {
                    'gl': result['final_gl'],
                    'rate': result['glycemic_rate'],
                    'risk': result['risk_level'],
                    'confidence': result.get('confidence_score', 0.87)
                }
                
                print(f"{formula_name}: GL={result['final_gl']:.1f}, 速率={result['glycemic_rate']:.1f}, 風險={result['risk_level']}")
                
            except Exception as e:
                print(f"{formula_name}: 計算錯誤 - {str(e)}")
                food_results[formula_name] = {
                    'gl': 0, 'rate': 0, 'risk': '錯誤', 'confidence': 0
                }
        
        results[food_name] = food_results
        
        # 更新餐食歷史
        gl_values = [r['gl'] for r in food_results.values() if r['gl'] > 0]
        avg_gl = np_mean(gl_values)
        meal_history.add_meal(0, avg_gl)
    
    return results

def analyze_formula_performance(results):
    """分析公式性能"""
    print("\n=== 公式性能分析 ===\n")
    
    formula_stats = {}
    
    for formula_name in ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'A10']:
        rates = []
        gls = []
        confidences = []
        
        for food_name, food_results in results.items():
            if formula_name in food_results and food_results[formula_name]['gl'] > 0:
                rates.append(food_results[formula_name]['rate'])
                gls.append(food_results[formula_name]['gl'])
                confidences.append(food_results[formula_name]['confidence'])
        
        if rates:
            formula_stats[formula_name] = {
                'avg_rate': np_mean(rates),
                'std_rate': np_std(rates),
                'avg_gl': np_mean(gls),
                'std_gl': np_std(gls),
                'avg_confidence': np_mean(confidences),
                'stability': 1.0 / (1.0 + np_std(rates))  # 穩定性指標
            }
    
    # 排序顯示
    sorted_formulas = sorted(formula_stats.items(), 
                           key=lambda x: x[1]['stability'], reverse=True)
    
    print("公式性能排名（按穩定性排序）:")
    print(f"{'公式':<4} {'平均升糖速率':<12} {'標準差':<8} {'穩定性':<8} {'置信度':<8}")
    print("-" * 50)
    
    for formula_name, stats in sorted_formulas:
        print(f"{formula_name:<4} {stats['avg_rate']:<12.2f} {stats['std_rate']:<8.2f} "
              f"{stats['stability']:<8.3f} {stats['avg_confidence']:<8.1%}")
    
    return formula_stats

def validate_with_clinical_data():
    """使用臨床數據驗證最佳公式"""
    print("\n=== 臨床數據驗證 ===\n")
    
    # 使用GetGoal研究數據
    validator = ClinicalDataValidator()
    
    # 測試A10公式（最複雜的）
    formula_a10 = FormulaA10()
    
    # 標準測試餐
    standard_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=78.0,
        fiber_g=3.0,
        protein_g=15.0,
        fat_g=8.0,
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        sugar_g=5.0,
        starch_g=73.0,
        water_content_pct=65.0,
        viscosity=1.5
    )
    
    meal_history = MealHistory()
    errors = []
    
    for i, patient_data in enumerate(validator.getgoal_patients[:3]):  # 測試前3個患者
        print(f"患者 {i+1}: 年齡{patient_data['age']:.0f}, BMI{patient_data['bmi']:.1f}, HbA1c{patient_data['hba1c']:.1f}%")
        
        # 創建擴展個人資料
        person = ExtendedPersonalProfile(
            age=int(patient_data['age']),
            weight_kg=patient_data['weight_kg'],
            height_cm=170.0,
            body_fat_pct=25.0 if patient_data['bmi'] > 30 else 20.0,
            hba1c=patient_data['hba1c'],
            fasting_glucose=patient_data['baseline_fpg'],
            diabetes_type=2,
            exercise_frequency=2,
            sleep_hours=7.0,
            # 估算的擴展參數
            microbiome_diversity=0.7 if patient_data['hba1c'] > 8.0 else 0.8,
            insulin_secretion_rate=0.8 if patient_data['hba1c'] > 8.0 else 1.0,
            hepatic_glycogen_pct=60.0 if patient_data['bmi'] > 30 else 70.0
        )
        
        context = MealContext(
            time_of_day=TimeOfDay.MIDDAY,
            fasting_hours=4.0,
            stress_level=3
        )
        
        # A10公式預測
        result = formula_a10.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        predicted_ppg_change = result['final_gl'] * 3.5
        actual_ppg_change = abs(patient_data['actual_ppg_change'])
        
        error = abs(predicted_ppg_change - actual_ppg_change) / actual_ppg_change * 100
        errors.append(error)
        
        print(f"  實際PPG變化: {actual_ppg_change:.1f} mg/dL")
        print(f"  A10預測變化: {predicted_ppg_change:.1f} mg/dL")
        print(f"  預測誤差: {error:.1f}%")
        print(f"  集成置信度: {result.get('ensemble_confidence', 0):.1%}")
        print()
    
    avg_error = np_mean(errors)
    print(f"A10公式平均預測誤差: {avg_error:.1f}%")
    
    if avg_error <= 9.0:
        print("🎉 達成目標！A10公式預測誤差 ≤ 9%")
    else:
        print(f"⚠️  需要進一步優化，目標誤差9%，當前{avg_error:.1f}%")
    
    return avg_error

if __name__ == "__main__":
    # 執行完整測試
    print("升糖基準指數公式系列 A1-A10 比較測試")
    print("目標：找出預測誤差 ≤ 9% 的最佳公式")
    print("=" * 60)
    
    # 測試所有公式
    results = test_all_formulas()
    
    # 分析性能
    stats = analyze_formula_performance(results)
    
    # 臨床驗證
    clinical_error = validate_with_clinical_data()
    
    print("\n=== 總結 ===")
    print("1. A1-A10公式系列已完成開發和測試")
    print("2. 每個公式都有獨特的創新技術")
    print("3. A10終極整合版結合了所有先進技術")
    print("4. 通過集成學習和多模型融合提升預測精度")
    print("5. 目標是將預測誤差降低到9%以下")
