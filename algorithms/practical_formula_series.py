"""
實用升糖基準指數公式系列 A1-A10
基於用戶可自行提供的參數設計
目標：在無外部設備情況下達到9%以下預測誤差

用戶可提供的參數：
- 基本生理：年齡、身高、體重、性別
- 健康指標：HbA1c（最近檢查報告）、血壓（家用血壓計）、靜息心率（自測）
- 生活習慣：運動頻率、睡眠時間、壓力水平、吸煙飲酒
- 家族史：糖尿病家族史
- 用餐習慣：進食速度、用餐頻率、咀嚼習慣
- 當前狀態：禁食時間、壓力水平、疲勞程度
"""

import math
from typing import Dict, List
from dataclasses import dataclass
from enum import Enum
from advanced_glycemic_calculator import (
    AdvancedGlycemicCalculator, FoodComposition, MealContext, MealHistory, 
    TimeOfDay, CarbType, ProcessingLevel, CookingMethod
)

@dataclass
class PracticalPersonalProfile:
    """實用個人資料 - 僅包含用戶可自行提供的參數"""
    # 基本生理參數
    age: int = 35
    height_cm: float = 170.0
    weight_kg: float = 70.0
    gender: str = "M"  # M/F
    
    # 健康指標（用戶可獲得）
    hba1c: float = 5.4  # 最近檢查報告
    blood_pressure_sys: int = 120  # 家用血壓計
    blood_pressure_dia: int = 80
    resting_hr: int = 70  # 自測靜息心率
    
    # 生活習慣（自報）
    exercise_frequency: int = 3  # 每週運動次數
    sleep_hours: float = 7.0  # 平均睡眠時間
    stress_level: int = 3  # 1-5級壓力水平
    smoking: bool = False  # 是否吸煙
    alcohol_frequency: int = 0  # 每週飲酒次數
    
    # 家族史（自知）
    family_diabetes_history: bool = False
    family_heart_disease: bool = False
    
    # 用餐習慣（自報）
    eating_speed: float = 1.0  # 進食速度 0.5-2.0
    meal_frequency: int = 3  # 每日用餐次數
    chewing_thoroughly: bool = True  # 是否充分咀嚼
    water_intake_liters: float = 2.0  # 每日飲水量
    
    # 當前狀態（實時）
    current_stress: int = 3  # 當前壓力 1-5
    fatigue_level: int = 2  # 疲勞程度 1-5
    sleep_quality_last_night: int = 3  # 昨晚睡眠質量 1-5
    
    @property
    def bmi(self) -> float:
        return self.weight_kg / (self.height_cm / 100) ** 2
    
    @property
    def is_overweight(self) -> bool:
        return self.bmi >= 24.0  # 亞洲標準
    
    @property
    def is_hypertensive(self) -> bool:
        return self.blood_pressure_sys >= 140 or self.blood_pressure_dia >= 90

class PracticalFormulaA1(AdvancedGlycemicCalculator):
    """A1實用基準公式 - 基於基本參數"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "實用A1 - 基準版"
        self.version = "1.0"
    
    def calculate_basic_metabolic_factor(self, person: PracticalPersonalProfile) -> float:
        """基於基本參數計算代謝因子"""
        factor = 1.0
        
        # 年齡影響
        if person.age > 50:
            factor *= 1.0 + (person.age - 50) * 0.01
        elif person.age < 30:
            factor *= 0.95
        
        # BMI影響
        if person.bmi > 25:
            factor *= 1.0 + (person.bmi - 25) * 0.03
        elif person.bmi < 20:
            factor *= 0.9
        
        # HbA1c影響
        if person.hba1c > 6.0:
            factor *= 1.0 + (person.hba1c - 6.0) * 0.2
        
        # 性別影響
        if person.gender == "F":
            factor *= 0.95  # 女性通常胰島素敏感性稍好
        
        return factor
    
    def calculate_lifestyle_factor(self, person: PracticalPersonalProfile) -> float:
        """計算生活習慣影響因子"""
        factor = 1.0
        
        # 運動頻率
        if person.exercise_frequency >= 4:
            factor *= 0.85  # 經常運動降低升糖反應
        elif person.exercise_frequency <= 1:
            factor *= 1.15  # 缺乏運動增加升糖反應
        
        # 睡眠影響
        if person.sleep_hours < 6:
            factor *= 1.2  # 睡眠不足增加升糖反應
        elif person.sleep_hours > 8:
            factor *= 0.95
        
        # 壓力影響
        factor *= 1.0 + (person.stress_level - 3) * 0.05
        
        # 吸煙影響
        if person.smoking:
            factor *= 1.15
        
        # 飲酒影響
        if person.alcohol_frequency > 3:
            factor *= 1.1
        
        return factor
    
    def calculate_eating_behavior_factor(self, person: PracticalPersonalProfile) -> float:
        """計算進食行為影響因子"""
        factor = 1.0
        
        # 進食速度
        factor *= person.eating_speed  # 直接影響
        
        # 咀嚼習慣
        if not person.chewing_thoroughly:
            factor *= 1.1  # 咀嚼不充分增加升糖
        
        # 用餐頻率
        if person.meal_frequency > 4:
            factor *= 0.9  # 少量多餐降低升糖峰值
        elif person.meal_frequency < 3:
            factor *= 1.1  # 餐次過少增加升糖峰值
        
        return factor
    
    def calculate_current_state_factor(self, person: PracticalPersonalProfile) -> float:
        """計算當前狀態影響因子"""
        factor = 1.0
        
        # 當前壓力
        factor *= 1.0 + (person.current_stress - 3) * 0.08
        
        # 疲勞程度
        factor *= 1.0 + (person.fatigue_level - 2) * 0.05
        
        # 睡眠質量
        factor *= 1.0 + (3 - person.sleep_quality_last_night) * 0.03
        
        return factor
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: PracticalPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 計算基礎GL
        base_gl = self._calculate_base_glycemic_load(food)
        
        # 應用各種修正因子
        metabolic_factor = self.calculate_basic_metabolic_factor(person)
        lifestyle_factor = self.calculate_lifestyle_factor(person)
        eating_factor = self.calculate_eating_behavior_factor(person)
        current_factor = self.calculate_current_state_factor(person)
        
        # 餐食情境調整
        context_factor = self._calculate_meal_context_factor(context, meal_history)
        
        # 計算最終GL
        final_gl = base_gl * metabolic_factor * lifestyle_factor * eating_factor * current_factor * context_factor
        
        # 計算升糖速率
        peak_time = self._estimate_peak_time(food, person)
        glycemic_rate = final_gl / peak_time
        
        # 評估風險
        safety_threshold = self._calculate_practical_safety_threshold(person)
        is_safe = glycemic_rate <= safety_threshold
        risk_level = self._assess_practical_risk(glycemic_rate, safety_threshold, person)
        
        return {
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'peak_time_hours': peak_time,
            'is_safe': is_safe,
            'risk_level': risk_level,
            'safety_threshold': safety_threshold,
            'confidence_score': 0.85,
            'metabolic_factor': metabolic_factor,
            'lifestyle_factor': lifestyle_factor,
            'eating_factor': eating_factor,
            'current_factor': current_factor,
            'context_factor': context_factor
        }
    
    def _calculate_base_glycemic_load(self, food: FoodComposition) -> float:
        """計算基礎升糖負荷"""
        # 基於碳水化合物類型和含量
        base_gi = 50  # 基準GI值
        
        if food.carb_type == CarbType.SIMPLE_SUGAR:
            base_gi = 70
        elif food.carb_type == CarbType.DISACCHARIDE:
            base_gi = 65
        elif food.carb_type == CarbType.STARCH:
            base_gi = 55
        elif food.carb_type == CarbType.COMPLEX_CARB:
            base_gi = 45
        elif food.carb_type == CarbType.RESISTANT_STARCH:
            base_gi = 30
        
        # 加工程度調整
        processing_multiplier = 1.0 + (food.processing_level.value - 1) * 0.1
        
        # 纖維含量調整
        fiber_adjustment = max(0.7, 1.0 - food.fiber_g * 0.02)
        
        # 脂肪蛋白質調整
        fat_protein_adjustment = max(0.8, 1.0 - (food.fat_g + food.protein_g) * 0.01)
        
        adjusted_gi = base_gi * processing_multiplier * fiber_adjustment * fat_protein_adjustment
        
        # 計算GL
        gl = (adjusted_gi * food.total_carbs_g) / 100
        
        return gl
    
    def _calculate_meal_context_factor(self, context: MealContext, meal_history: MealHistory) -> float:
        """計算餐食情境因子"""
        factor = 1.0
        
        # 禁食時間影響
        if context.fasting_hours > 8:
            factor *= 1.1
        elif context.fasting_hours < 3:
            factor *= 0.9
        
        # 時間段影響
        if context.time_of_day == TimeOfDay.MORNING:
            factor *= 1.05  # 晨間胰島素敏感性較低
        elif context.time_of_day == TimeOfDay.EVENING:
            factor *= 1.02
        
        # 近期餐食影響
        recent_meals = len([m for m in meal_history.meals if m[0] > -4])  # 4小時內
        if recent_meals > 1:
            factor *= 0.9  # 近期有進食降低升糖反應
        
        return factor
    
    def _estimate_peak_time(self, food: FoodComposition, person: PracticalPersonalProfile) -> float:
        """估算血糖峰值時間"""
        base_time = 2.0  # 基礎2小時
        
        # 食物類型影響
        if food.carb_type == CarbType.SIMPLE_SUGAR:
            base_time = 1.0
        elif food.carb_type == CarbType.DISACCHARIDE:
            base_time = 1.5
        elif food.carb_type == CarbType.RESISTANT_STARCH:
            base_time = 3.0
        elif food.carb_type == CarbType.COMPLEX_CARB:
            base_time = 2.5
        
        # 脂肪含量延緩
        if food.fat_g > 15:
            base_time *= 1.3
        
        # 個人因素
        if person.age > 60:
            base_time *= 1.1
        
        if person.bmi > 30:
            base_time *= 1.05
        
        return base_time
    
    def _calculate_practical_safety_threshold(self, person: PracticalPersonalProfile) -> float:
        """計算實用安全閾值"""
        base_threshold = 5.0
        
        # 根據HbA1c調整
        if person.hba1c > 7.0:
            base_threshold = 4.0
        elif person.hba1c < 5.5:
            base_threshold = 6.0
        
        # 年齡調整
        if person.age > 65:
            base_threshold *= 0.9
        
        # BMI調整
        if person.bmi > 30:
            base_threshold *= 0.9
        
        return base_threshold
    
    def _assess_practical_risk(self, glycemic_rate: float, threshold: float, 
                              person: PracticalPersonalProfile) -> str:
        """評估實用風險等級"""
        if glycemic_rate <= threshold * 0.5:
            return "極低風險"
        elif glycemic_rate <= threshold:
            return "低風險"
        elif glycemic_rate <= threshold * 1.5:
            return "中風險"
        elif glycemic_rate <= threshold * 2.0:
            return "高風險"
        else:
            return "危險"

class PracticalFormulaA2(PracticalFormulaA1):
    """A2實用公式 - 強化血壓心率影響"""

    def __init__(self):
        super().__init__()
        self.formula_name = "實用A2 - 血壓心率強化版"
        self.version = "2.0"

    def calculate_cardiovascular_factor(self, person: PracticalPersonalProfile) -> float:
        """計算心血管影響因子"""
        factor = 1.0

        # 血壓影響
        if person.blood_pressure_sys > 140:
            factor *= 1.0 + (person.blood_pressure_sys - 140) * 0.005
        if person.blood_pressure_dia > 90:
            factor *= 1.0 + (person.blood_pressure_dia - 90) * 0.008

        # 靜息心率影響
        if person.resting_hr > 80:
            factor *= 1.0 + (person.resting_hr - 80) * 0.01
        elif person.resting_hr < 60:
            factor *= 0.95  # 運動員心率，代謝較好

        # 家族心血管病史
        if person.family_heart_disease:
            factor *= 1.05

        return factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: PracticalPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用心血管因子
        cv_factor = self.calculate_cardiovascular_factor(person)
        result['final_gl'] *= cv_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        result['cardiovascular_factor'] = cv_factor

        return result

class PracticalFormulaA3(PracticalFormulaA1):
    """A3實用公式 - 強化家族史和遺傳風險"""

    def __init__(self):
        super().__init__()
        self.formula_name = "實用A3 - 遺傳風險強化版"
        self.version = "3.0"

    def calculate_genetic_risk_factor(self, person: PracticalPersonalProfile) -> float:
        """計算遺傳風險因子"""
        factor = 1.0

        # 糖尿病家族史
        if person.family_diabetes_history:
            factor *= 1.15

        # 心血管疾病家族史
        if person.family_heart_disease:
            factor *= 1.08

        # 性別和年齡交互作用
        if person.gender == "F" and person.age > 50:
            factor *= 1.05  # 更年期後風險增加
        elif person.gender == "M" and person.age > 45:
            factor *= 1.03  # 男性中年後風險增加

        # BMI和遺傳風險交互作用
        if person.family_diabetes_history and person.bmi > 25:
            factor *= 1.1  # 遺傳風險+肥胖的協同效應

        return factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: PracticalPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用遺傳風險因子
        genetic_factor = self.calculate_genetic_risk_factor(person)
        result['final_gl'] *= genetic_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        result['genetic_risk_factor'] = genetic_factor

        return result

class PracticalFormulaA4(PracticalFormulaA1):
    """A4實用公式 - 強化睡眠和壓力影響"""

    def __init__(self):
        super().__init__()
        self.formula_name = "實用A4 - 睡眠壓力強化版"
        self.version = "4.0"

    def calculate_stress_sleep_factor(self, person: PracticalPersonalProfile) -> float:
        """計算壓力睡眠綜合因子"""
        factor = 1.0

        # 慢性壓力影響
        chronic_stress = person.stress_level
        factor *= 1.0 + (chronic_stress - 3) * 0.12

        # 急性壓力影響
        acute_stress = person.current_stress
        factor *= 1.0 + (acute_stress - 3) * 0.08

        # 睡眠時間影響（非線性）
        sleep_hours = person.sleep_hours
        if sleep_hours < 6:
            factor *= 1.0 + (6 - sleep_hours) * 0.15  # 睡眠不足嚴重影響
        elif sleep_hours > 9:
            factor *= 1.0 + (sleep_hours - 9) * 0.05  # 過度睡眠輕微影響

        # 睡眠質量影響
        sleep_quality = person.sleep_quality_last_night
        factor *= 1.0 + (3 - sleep_quality) * 0.06

        # 疲勞程度影響
        fatigue = person.fatigue_level
        factor *= 1.0 + (fatigue - 2) * 0.08

        # 壓力和睡眠的交互作用
        if chronic_stress >= 4 and sleep_hours < 6:
            factor *= 1.2  # 高壓力+睡眠不足的協同負面效應

        return factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: PracticalPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用壓力睡眠因子
        stress_sleep_factor = self.calculate_stress_sleep_factor(person)
        result['final_gl'] *= stress_sleep_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        result['stress_sleep_factor'] = stress_sleep_factor

        return result

class PracticalFormulaA5(PracticalFormulaA1):
    """A5實用公式 - 強化運動和生活習慣"""

    def __init__(self):
        super().__init__()
        self.formula_name = "實用A5 - 運動習慣強化版"
        self.version = "5.0"

    def calculate_exercise_lifestyle_factor(self, person: PracticalPersonalProfile) -> float:
        """計算運動生活習慣綜合因子"""
        factor = 1.0

        # 運動頻率的非線性影響
        exercise_freq = person.exercise_frequency
        if exercise_freq == 0:
            factor *= 1.25  # 完全不運動
        elif exercise_freq <= 2:
            factor *= 1.1   # 運動不足
        elif exercise_freq >= 5:
            factor *= 0.8   # 經常運動
        else:
            factor *= 0.9   # 適度運動

        # 吸煙的嚴重影響
        if person.smoking:
            factor *= 1.2

        # 飲酒頻率影響
        alcohol_freq = person.alcohol_frequency
        if alcohol_freq > 5:
            factor *= 1.15  # 頻繁飲酒
        elif alcohol_freq > 2:
            factor *= 1.05  # 適度飲酒

        # 水分攝取影響
        if person.water_intake_liters < 1.5:
            factor *= 1.08  # 水分不足影響代謝
        elif person.water_intake_liters > 3.0:
            factor *= 0.98  # 充足水分有利代謝

        # 運動和BMI的交互作用
        if exercise_freq >= 4 and person.bmi > 25:
            factor *= 0.9   # 運動對肥胖者更有益

        return factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: PracticalPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用運動生活習慣因子
        exercise_factor = self.calculate_exercise_lifestyle_factor(person)
        result['final_gl'] *= exercise_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        result['exercise_lifestyle_factor'] = exercise_factor

        return result
