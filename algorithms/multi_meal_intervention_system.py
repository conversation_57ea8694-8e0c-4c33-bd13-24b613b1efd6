"""
多餐干預升糖基準指數系統
目標：90%人群達到10%以下誤差
功能：短期多餐模型 + 營養干預 + 份量計算
"""

import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

@dataclass
class StandardFoodPortion:
    """標準食物份量定義"""
    food_name: str
    standard_weight_g: float  # 標準一份重量
    carbs_per_portion: float  # 每份碳水化合物
    protein_per_portion: float  # 每份蛋白質
    fat_per_portion: float  # 每份脂肪
    fiber_per_portion: float  # 每份纖維
    
class InterventionType(Enum):
    """干預類型"""
    PROTEIN = "蛋白質"
    FIBER = "纖維"
    UNSATURATED_FAT = "不飽和脂肪"
    ENZYME_INHIBITOR = "抑制酶"
    CHROMIUM = "鉻"
    VINEGAR = "醋"

@dataclass
class NutritionIntervention:
    """營養干預"""
    intervention_type: InterventionType
    amount_g: float  # 干預量(克)
    timing: str  # 時機：before/with/after
    effectiveness: float = 1.0  # 有效性係數

@dataclass
class MealRecord:
    """餐食記錄"""
    meal_time: datetime
    food_portions: Dict[str, float]  # 食物名稱: 份數
    interventions: List[NutritionIntervention]
    predicted_gl: float
    actual_bg_change: Optional[float] = None
    prediction_error: Optional[float] = None
    positive_error: Optional[float] = None  # 正向誤差

@dataclass
class DailyMealPlan:
    """每日餐食計劃"""
    date: datetime
    meals: List[MealRecord] = field(default_factory=list)
    cumulative_gl: float = 0.0
    intervention_effectiveness: float = 1.0

class FoodPortionDatabase:
    """食物份量數據庫"""
    
    def __init__(self):
        self.portions = {
            # 主食類 (標準一份 = 15g碳水)
            "白米飯": StandardFoodPortion("白米飯", 55, 15, 2.5, 0.3, 0.4),
            "糙米飯": StandardFoodPortion("糙米飯", 55, 15, 2.8, 0.9, 1.8),
            "白麵條": StandardFoodPortion("白麵條", 40, 15, 3.0, 0.5, 0.8),
            "全麥麵包": StandardFoodPortion("全麥麵包", 30, 15, 3.5, 1.2, 2.5),
            "燕麥": StandardFoodPortion("燕麥", 20, 15, 3.0, 1.5, 3.0),
            
            # 蛋白質類 (標準一份 = 7g蛋白質)
            "雞胸肉": StandardFoodPortion("雞胸肉", 30, 0, 7, 1.5, 0),
            "魚肉": StandardFoodPortion("魚肉", 30, 0, 7, 2.0, 0),
            "豆腐": StandardFoodPortion("豆腐", 80, 1, 7, 3.0, 1.0),
            "雞蛋": StandardFoodPortion("雞蛋", 50, 0.5, 7, 5.0, 0),
            
            # 蔬菜類 (標準一份 = 5g碳水)
            "青菜": StandardFoodPortion("青菜", 100, 5, 2, 0.2, 2.5),
            "花椰菜": StandardFoodPortion("花椰菜", 100, 5, 3, 0.3, 2.8),
            "胡蘿蔔": StandardFoodPortion("胡蘿蔔", 80, 5, 1, 0.2, 2.0),
            
            # 水果類 (標準一份 = 15g碳水)
            "蘋果": StandardFoodPortion("蘋果", 120, 15, 0.3, 0.2, 2.4),
            "香蕉": StandardFoodPortion("香蕉", 80, 15, 1.0, 0.3, 1.8),
            "葡萄": StandardFoodPortion("葡萄", 85, 15, 0.6, 0.2, 0.8),
            
            # 油脂類 (標準一份 = 5g脂肪)
            "橄欖油": StandardFoodPortion("橄欖油", 5, 0, 0, 5, 0),
            "堅果": StandardFoodPortion("堅果", 8, 2, 2, 5, 1.5),
        }
    
    def calculate_portions(self, food_name: str, weight_g: float) -> Tuple[float, Dict[str, float]]:
        """計算食物份數和營養成分"""
        if food_name not in self.portions:
            raise ValueError(f"食物 {food_name} 不在數據庫中")
        
        standard = self.portions[food_name]
        portions = weight_g / standard.standard_weight_g
        
        nutrition = {
            'carbs': standard.carbs_per_portion * portions,
            'protein': standard.protein_per_portion * portions,
            'fat': standard.fat_per_portion * portions,
            'fiber': standard.fiber_per_portion * portions
        }
        
        return portions, nutrition

class InterventionCalculator:
    """營養干預計算器"""
    
    def __init__(self):
        # 干預效果係數 (基於科學研究)
        self.intervention_effects = {
            InterventionType.PROTEIN: {
                'gl_reduction_per_g': 0.015,  # 每克蛋白質降低GL的比例
                'max_reduction': 0.4,  # 最大降低40%
                'optimal_ratio': 0.3   # 最佳蛋白質/碳水比例
            },
            InterventionType.FIBER: {
                'gl_reduction_per_g': 0.025,  # 每克纖維降低GL的比例
                'max_reduction': 0.5,  # 最大降低50%
                'optimal_amount': 10   # 最佳纖維量(g)
            },
            InterventionType.UNSATURATED_FAT: {
                'gl_reduction_per_g': 0.012,  # 每克不飽和脂肪降低GL的比例
                'max_reduction': 0.3,  # 最大降低30%
                'optimal_amount': 15   # 最佳脂肪量(g)
            },
            InterventionType.ENZYME_INHIBITOR: {
                'gl_reduction_base': 0.2,  # 基礎降低20%
                'max_reduction': 0.35,  # 最大降低35%
                'effective_dose': 1.0   # 有效劑量(g)
            },
            InterventionType.CHROMIUM: {
                'gl_reduction_base': 0.1,  # 基礎降低10%
                'max_reduction': 0.25,  # 最大降低25%
                'effective_dose': 0.0002  # 有效劑量(200mcg)
            },
            InterventionType.VINEGAR: {
                'gl_reduction_base': 0.15,  # 基礎降低15%
                'max_reduction': 0.3,   # 最大降低30%
                'effective_dose': 15    # 有效劑量(15ml)
            }
        }
    
    def calculate_intervention_effect(self, interventions: List[NutritionIntervention], 
                                    base_carbs: float) -> float:
        """計算干預效果"""
        total_reduction = 0.0
        
        for intervention in interventions:
            effect_params = self.intervention_effects[intervention.intervention_type]
            
            if intervention.intervention_type == InterventionType.PROTEIN:
                # 蛋白質干預：基於蛋白質/碳水比例
                protein_carb_ratio = intervention.amount_g / max(base_carbs, 1)
                reduction = min(
                    protein_carb_ratio * effect_params['gl_reduction_per_g'] * 100,
                    effect_params['max_reduction']
                )
            
            elif intervention.intervention_type == InterventionType.FIBER:
                # 纖維干預：基於纖維量
                reduction = min(
                    intervention.amount_g * effect_params['gl_reduction_per_g'],
                    effect_params['max_reduction']
                )
            
            elif intervention.intervention_type == InterventionType.UNSATURATED_FAT:
                # 不飽和脂肪干預
                reduction = min(
                    intervention.amount_g * effect_params['gl_reduction_per_g'],
                    effect_params['max_reduction']
                )
            
            else:
                # 其他干預（酶抑制劑、鉻、醋等）
                dose_ratio = intervention.amount_g / effect_params['effective_dose']
                reduction = min(
                    effect_params['gl_reduction_base'] * dose_ratio,
                    effect_params['max_reduction']
                )
            
            # 時機調整
            timing_multiplier = {
                'before': 1.2,  # 餐前效果更好
                'with': 1.0,    # 餐中標準效果
                'after': 0.7    # 餐後效果較差
            }.get(intervention.timing, 1.0)
            
            total_reduction += reduction * timing_multiplier * intervention.effectiveness
        
        # 多種干預的協同效應（非線性疊加）
        if len(interventions) > 1:
            synergy_factor = 1.0 + (len(interventions) - 1) * 0.1
            total_reduction *= synergy_factor
        
        return min(total_reduction, 0.7)  # 最大降低70%

class MultiMealGlycemicSystem:
    """多餐升糖系統"""
    
    def __init__(self):
        self.food_db = FoodPortionDatabase()
        self.intervention_calc = InterventionCalculator()
        self.meal_history = []
        
        # 優化的校準係數（目標90%人群10%誤差）
        self.calibration = {
            'base_multiplier': 1.8,      # 進一步優化的基礎係數
            'multi_meal_decay': 0.85,    # 多餐衰減係數
            'intervention_boost': 1.15,  # 干預效果增強
            'portion_precision': 1.05,   # 份量精度調整
            'individual_variance': 0.92  # 個體差異調整
        }
        
        # 誤差追蹤
        self.error_history = []
        self.positive_error_history = []
    
    def calculate_meal_gl(self, food_weights: Dict[str, float], 
                         interventions: List[NutritionIntervention],
                         person_profile: Dict) -> Dict[str, float]:
        """計算單餐升糖負荷"""
        total_carbs = 0.0
        total_protein = 0.0
        total_fat = 0.0
        total_fiber = 0.0
        portion_details = {}
        
        # 計算各食物的份數和營養成分
        for food_name, weight in food_weights.items():
            portions, nutrition = self.food_db.calculate_portions(food_name, weight)
            portion_details[food_name] = {
                'weight_g': weight,
                'portions': portions,
                'nutrition': nutrition
            }
            
            total_carbs += nutrition['carbs']
            total_protein += nutrition['protein']
            total_fat += nutrition['fat']
            total_fiber += nutrition['fiber']
        
        # 基礎GL計算
        base_gl = total_carbs * 0.7  # 基礎升糖係數
        
        # 食物內在調整
        fiber_adjustment = max(0.5, 1.0 - total_fiber * 0.03)
        protein_adjustment = max(0.7, 1.0 - total_protein * 0.02)
        fat_adjustment = max(0.6, 1.0 - total_fat * 0.015)
        
        adjusted_gl = base_gl * fiber_adjustment * protein_adjustment * fat_adjustment
        
        # 個人因子調整
        personal_factor = self._calculate_personal_factor(person_profile)
        
        # 干預效果計算
        intervention_reduction = self.intervention_calc.calculate_intervention_effect(
            interventions, total_carbs
        )
        
        # 最終GL計算
        final_gl = (adjusted_gl * personal_factor * 
                   (1 - intervention_reduction) * 
                   self.calibration['base_multiplier'] * 
                   self.calibration['intervention_boost'])
        
        return {
            'base_gl': base_gl,
            'adjusted_gl': adjusted_gl,
            'final_gl': final_gl,
            'intervention_reduction': intervention_reduction,
            'personal_factor': personal_factor,
            'portion_details': portion_details,
            'total_nutrition': {
                'carbs': total_carbs,
                'protein': total_protein,
                'fat': total_fat,
                'fiber': total_fiber
            }
        }
    
    def calculate_multi_meal_effect(self, daily_plan: DailyMealPlan, 
                                  current_meal_time: datetime) -> float:
        """計算多餐累積效應"""
        cumulative_factor = 1.0
        
        # 計算前面餐食的影響
        for meal in daily_plan.meals:
            time_diff = (current_meal_time - meal.meal_time).total_seconds() / 3600  # 小時
            
            if 0 < time_diff <= 6:  # 6小時內的餐食有影響
                # 時間衰減效應
                decay_factor = math.exp(-time_diff / 3)
                
                # GL影響強度
                gl_influence = meal.predicted_gl * decay_factor * self.calibration['multi_meal_decay']
                
                # 累積效應（非線性）
                cumulative_factor += gl_influence * 0.1
        
        return min(cumulative_factor, 2.0)  # 最大2倍影響
    
    def _calculate_personal_factor(self, profile: Dict) -> float:
        """計算個人因子"""
        factor = 1.0
        
        # HbA1c影響
        hba1c = profile.get('hba1c', 5.4)
        if hba1c > 7.0:
            factor *= 1.0 + (hba1c - 7.0) * 0.12
        
        # BMI影響
        bmi = profile.get('bmi', 23.0)
        if bmi > 25:
            factor *= 1.0 + (bmi - 25) * 0.03
        
        # 年齡影響
        age = profile.get('age', 35)
        if age > 50:
            factor *= 1.0 + (age - 50) * 0.008
        
        # 運動頻率
        exercise = profile.get('exercise_frequency', 3)
        if exercise >= 4:
            factor *= 0.85
        elif exercise <= 1:
            factor *= 1.15
        
        return factor * self.calibration['individual_variance']
    
    def predict_meal_impact(self, food_weights: Dict[str, float],
                          interventions: List[NutritionIntervention],
                          person_profile: Dict,
                          meal_time: datetime,
                          daily_plan: DailyMealPlan) -> Dict:
        """預測餐食影響"""
        # 計算單餐GL
        meal_result = self.calculate_meal_gl(food_weights, interventions, person_profile)
        
        # 計算多餐效應
        multi_meal_factor = self.calculate_multi_meal_effect(daily_plan, meal_time)
        
        # 最終預測
        final_gl = meal_result['final_gl'] * multi_meal_factor
        predicted_bg_change = final_gl * 2.5  # GL轉血糖變化
        
        # 風險評估
        risk_level = self._assess_risk(final_gl, predicted_bg_change)
        
        return {
            'predicted_gl': final_gl,
            'predicted_bg_change': predicted_bg_change,
            'risk_level': risk_level,
            'multi_meal_factor': multi_meal_factor,
            'meal_details': meal_result,
            'confidence': self._calculate_confidence()
        }
    
    def _assess_risk(self, gl: float, bg_change: float) -> str:
        """評估風險等級"""
        if gl < 8 and bg_change < 30:
            return "極低風險"
        elif gl < 15 and bg_change < 50:
            return "低風險"
        elif gl < 25 and bg_change < 80:
            return "中風險"
        elif gl < 35 and bg_change < 120:
            return "高風險"
        else:
            return "危險"
    
    def _calculate_confidence(self) -> float:
        """計算預測置信度"""
        if len(self.error_history) < 5:
            return 0.85
        
        recent_errors = self.error_history[-10:]
        avg_error = sum(recent_errors) / len(recent_errors)
        
        # 誤差越小，置信度越高
        confidence = max(0.5, 1.0 - avg_error / 100)
        return min(confidence, 0.95)
    
    def update_with_actual_result(self, predicted_gl: float, actual_bg_change: float):
        """更新實際結果"""
        # 轉換實際血糖變化為GL
        actual_gl = actual_bg_change / 2.5
        
        # 計算誤差
        error = abs(predicted_gl - actual_gl) / actual_gl * 100
        self.error_history.append(error)
        
        # 計算正向誤差（預測高於實際）
        if predicted_gl > actual_gl:
            positive_error = (predicted_gl - actual_gl) / actual_gl * 100
            self.positive_error_history.append(positive_error)
        
        # 自適應校準
        self._adaptive_calibration(predicted_gl, actual_gl)
        
        # 保持歷史記錄在合理範圍
        if len(self.error_history) > 100:
            self.error_history.pop(0)
        if len(self.positive_error_history) > 100:
            self.positive_error_history.pop(0)
    
    def _adaptive_calibration(self, predicted: float, actual: float):
        """自適應校準"""
        error_ratio = predicted / max(actual, 0.1)
        
        # 調整基礎係數
        if error_ratio > 1.1:  # 預測偏高
            self.calibration['base_multiplier'] *= 0.98
        elif error_ratio < 0.9:  # 預測偏低
            self.calibration['base_multiplier'] *= 1.02
        
        # 限制係數範圍
        self.calibration['base_multiplier'] = max(0.5, min(3.0, self.calibration['base_multiplier']))
    
    def get_performance_stats(self) -> Dict:
        """獲取性能統計"""
        if not self.error_history:
            return {'avg_error': 0, 'accuracy_rate': 0, 'positive_error_rate': 0}
        
        avg_error = sum(self.error_history) / len(self.error_history)
        accuracy_rate = sum(1 for e in self.error_history if e <= 10.0) / len(self.error_history)
        
        positive_error_rate = 0
        if self.positive_error_history:
            positive_error_rate = len(self.positive_error_history) / len(self.error_history)
        
        return {
            'avg_error': avg_error,
            'accuracy_rate': accuracy_rate,
            'positive_error_rate': positive_error_rate,
            'total_predictions': len(self.error_history),
            'current_calibration': self.calibration.copy()
        }
