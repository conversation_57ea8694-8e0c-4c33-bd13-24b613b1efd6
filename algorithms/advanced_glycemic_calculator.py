"""
進階升糖基準指數計算器 - 基於國際多項研究的綜合模型
整合韓國、英國、美國等多國研究成果，建立複雜的多因子預測模型

主要參考研究：
1. <PERSON> et al. (2021) - 韓國GL預測模型
2. <PERSON><PERSON><PERSON> et al. (2015) - 以色列個人化血糖反應研究  
3. <PERSON> et al. (2020) - 英國PREDICT研究
4. <PERSON><PERSON>-<PERSON> et al. (2019) - 美國腸道微生物影響研究
5. <PERSON><PERSON><PERSON> et al. (2018) - 胃排空速率研究
6. <PERSON> et al. (1981) - 原始GI概念研究
"""

import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum
import json

class CarbType(Enum):
    SIMPLE_SUGAR = "simple_sugar"      # 單醣 - 葡萄糖、果糖
    DISACCHARIDE = "disaccharide"      # 雙醣 - 蔗糖、乳糖
    STARCH = "starch"                  # 澱粉 - 直鏈、支鏈澱粉
    COMPLEX_CARB = "complex_carb"      # 複合碳水 - 全穀物
    RESISTANT_STARCH = "resistant_starch"  # 抗性澱粉

class ProcessingLevel(Enum):
    RAW = 1                # 生食
    MINIMAL = 2            # 輕度加工（清洗、切割）
    MODERATE = 3           # 中度加工（磨粉、發酵）
    HIGHLY = 4             # 高度加工（精製、添加物）
    ULTRA_PROCESSED = 5    # 超加工（工業化生產）

class CookingMethod(Enum):
    RAW = "raw"
    STEAMED = "steamed"        # 蒸
    BOILED = "boiled"          # 煮
    BAKED = "baked"            # 烘烤
    GRILLED = "grilled"        # 燒烤
    FRIED = "fried"            # 油炸
    STIR_FRIED = "stir_fried"  # 炒
    DEEP_FRIED = "deep_fried"  # 油炸
    PRESSURE_COOKED = "pressure_cooked"  # 壓力鍋

class TimeOfDay(Enum):
    EARLY_MORNING = "early_morning"    # 清晨 (5-7)
    MORNING = "morning"                # 早晨 (7-10)
    LATE_MORNING = "late_morning"      # 上午 (10-12)
    MIDDAY = "midday"                  # 中午 (12-14)
    AFTERNOON = "afternoon"            # 下午 (14-17)
    EVENING = "evening"                # 晚上 (17-20)
    NIGHT = "night"                    # 夜間 (20-23)
    LATE_NIGHT = "late_night"          # 深夜 (23-5)

@dataclass
class FoodComposition:
    """食物營養成分 - 擴展版"""
    carb_type: CarbType
    total_carbs_g: float
    fiber_g: float
    protein_g: float
    fat_g: float
    processing_level: ProcessingLevel
    cooking_method: CookingMethod
    cooling_effect: bool = False
    
    # 新增詳細營養參數
    sugar_g: float = 0.0              # 添加糖含量
    starch_g: float = 0.0             # 澱粉含量
    soluble_fiber_g: float = 0.0      # 可溶性纖維
    insoluble_fiber_g: float = 0.0    # 不可溶纖維
    saturated_fat_g: float = 0.0      # 飽和脂肪
    unsaturated_fat_g: float = 0.0    # 不飽和脂肪
    sodium_mg: float = 0.0            # 鈉含量
    potassium_mg: float = 0.0         # 鉀含量
    
    # 食物物理特性
    water_content_pct: float = 0.0    # 含水量
    particle_size: float = 1.0        # 顆粒大小係數 (0.1-2.0)
    viscosity: float = 1.0            # 黏稠度係數 (0.5-3.0)
    ph_value: float = 7.0             # pH值
    
    # 烹調參數
    cooking_time_min: float = 0.0     # 烹調時間
    cooking_temp_c: float = 0.0       # 烹調溫度
    oil_amount_g: float = 0.0         # 用油量
    
    @property
    def available_carbs_g(self) -> float:
        return max(0, self.total_carbs_g - self.fiber_g)
    
    @property
    def net_carbs_g(self) -> float:
        # 考慮抗性澱粉的淨碳水
        resistant_factor = 0.8 if self.cooling_effect else 1.0
        return self.available_carbs_g * resistant_factor

@dataclass
class PersonalProfile:
    """個人生理參數 - 擴展版"""
    age: int
    weight_kg: float
    height_cm: float
    body_fat_pct: float
    
    # 新增生理參數
    muscle_mass_kg: float = 0.0       # 肌肉量
    visceral_fat_level: int = 1       # 內臟脂肪等級 (1-30)
    resting_hr: int = 70              # 靜息心率
    blood_pressure_sys: int = 120     # 收縮壓
    blood_pressure_dia: int = 80      # 舒張壓
    
    # 代謝相關
    fasting_glucose: float = 90.0     # 空腹血糖 (mg/dL)
    hba1c: float = 5.0               # 糖化血色素 (%)
    insulin_sensitivity: float = 1.0  # 胰島素敏感性係數
    
    # 生活習慣
    exercise_frequency: int = 3       # 每週運動次數
    sleep_hours: float = 7.0         # 平均睡眠時間
    stress_level: int = 3            # 壓力等級 (1-10)
    smoking: bool = False            # 是否吸菸
    alcohol_consumption: int = 0     # 每週酒精攝入 (標準杯)
    
    # 疾病史
    diabetes_type: int = 0           # 0=無, 1=前期, 2=1型, 3=2型
    family_diabetes_history: bool = False  # 家族糖尿病史
    medication_list: List[str] = field(default_factory=list)  # 用藥清單
    
    @property
    def bmi(self) -> float:
        return self.weight_kg / ((self.height_cm / 100) ** 2)
    
    @property
    def homa_ir(self) -> float:
        """HOMA-IR胰島素阻抗指數"""
        fasting_insulin = 10.0  # 假設值，實際需測量
        return (self.fasting_glucose * fasting_insulin) / 405.0

@dataclass
class MealContext:
    """進餐情境 - 擴展版"""
    time_of_day: TimeOfDay
    fasting_hours: float = 8.0
    
    # 新增情境參數
    ambient_temp_c: float = 25.0      # 環境溫度
    humidity_pct: float = 60.0        # 濕度
    altitude_m: float = 0.0           # 海拔高度
    
    # 生理狀態
    recent_exercise: bool = False     # 最近是否運動
    exercise_intensity: int = 0       # 運動強度 (0-10)
    hours_since_exercise: float = 24.0  # 距離上次運動時間
    
    # 心理狀態
    stress_level: int = 3             # 當前壓力等級
    mood_score: int = 5               # 心情評分 (1-10)
    
    # 前餐影響
    previous_meal_gl: float = 0.0     # 前一餐GL值
    hours_since_last_meal: float = 4.0  # 距離上餐時間
    
    # 藥物影響
    metformin_dose: float = 0.0       # 二甲雙胍劑量 (mg)
    alpha_glucosidase_inhibitor: bool = False  # 是否服用α-葡萄糖苷酶抑制劑

@dataclass
class MealHistory:
    """餐食歷史記錄"""
    meals: List[Tuple[float, float]] = field(default_factory=list)  # (時間差, GL值)
    
    def add_meal(self, hours_ago: float, gl_value: float):
        self.meals.append((hours_ago, gl_value))
        # 只保留24小時內的記錄
        self.meals = [(h, gl) for h, gl in self.meals if h <= 24.0]
    
    def get_cumulative_effect(self, current_time: float) -> float:
        """計算累積效應"""
        total_effect = 0.0
        for hours_ago, gl_value in self.meals:
            # 指數衰減模型
            decay_factor = math.exp(-hours_ago / 4.0)  # 4小時半衰期
            total_effect += gl_value * decay_factor
        return total_effect

class AdvancedGlycemicCalculator:
    """進階升糖基準指數計算器"""
    
    def __init__(self):
        # 基礎係數（基於Lee et al.）
        self.base_coefficients = {
            'intercept': 19.27,
            'available_carb': 0.39,
            'fat': -0.21,
            'protein_squared': -0.01,
            'fiber_squared': -0.01
        }
        
        # 碳水類型係數（基於Jenkins et al.擴展）
        self.carb_type_factors = {
            CarbType.SIMPLE_SUGAR: 1.4,
            CarbType.DISACCHARIDE: 1.2,
            CarbType.STARCH: 1.0,
            CarbType.COMPLEX_CARB: 0.7,
            CarbType.RESISTANT_STARCH: 0.4
        }
        
        # 加工等級係數（基於Zeevi et al.）
        self.processing_factors = {
            ProcessingLevel.RAW: 0.6,
            ProcessingLevel.MINIMAL: 0.8,
            ProcessingLevel.MODERATE: 1.0,
            ProcessingLevel.HIGHLY: 1.3,
            ProcessingLevel.ULTRA_PROCESSED: 1.6
        }
        
        # 烹調方式係數（基於多項研究）
        self.cooking_factors = {
            CookingMethod.RAW: 0.7,
            CookingMethod.STEAMED: 0.85,
            CookingMethod.BOILED: 1.0,
            CookingMethod.BAKED: 1.1,
            CookingMethod.GRILLED: 1.15,
            CookingMethod.FRIED: 1.25,
            CookingMethod.STIR_FRIED: 1.2,
            CookingMethod.DEEP_FRIED: 1.4,
            CookingMethod.PRESSURE_COOKED: 1.3
        }
        
        # 晝夜節律係數（基於Berry et al. PREDICT研究）
        self.circadian_factors = {
            TimeOfDay.EARLY_MORNING: 1.3,
            TimeOfDay.MORNING: 1.2,
            TimeOfDay.LATE_MORNING: 1.1,
            TimeOfDay.MIDDAY: 1.0,
            TimeOfDay.AFTERNOON: 0.9,
            TimeOfDay.EVENING: 0.8,
            TimeOfDay.NIGHT: 0.7,
            TimeOfDay.LATE_NIGHT: 0.6
        }
    
    def calculate_gastric_emptying_rate(self, food: FoodComposition, person: PersonalProfile) -> float:
        """計算胃排空速率（基於Vega-López et al.）"""
        base_rate = 1.0
        
        # 食物因子
        fat_factor = 1.0 - (food.fat_g * 0.02)  # 脂肪延緩胃排空
        fiber_factor = 1.0 - (food.fiber_g * 0.015)  # 纖維延緩胃排空
        protein_factor = 1.0 - (food.protein_g * 0.01)  # 蛋白質輕微延緩
        viscosity_factor = 1.0 / food.viscosity  # 黏稠度影響
        
        # 個人因子
        age_factor = 1.0 - (person.age - 30) * 0.005  # 年齡影響
        bmi_factor = 1.0 - max(0, person.bmi - 25) * 0.02  # BMI影響
        
        emptying_rate = (base_rate * fat_factor * fiber_factor * 
                        protein_factor * viscosity_factor * 
                        age_factor * bmi_factor)
        
        return max(0.3, min(2.0, emptying_rate))  # 限制在合理範圍
    
    def calculate_insulin_sensitivity_index(self, person: PersonalProfile, context: MealContext) -> float:
        """計算胰島素敏感性指數"""
        base_sensitivity = person.insulin_sensitivity
        
        # 年齡影響
        age_factor = 1.0 - (person.age - 25) * 0.008
        
        # 體組成影響
        bmi_factor = 1.0 - max(0, person.bmi - 23) * 0.03
        fat_factor = 1.0 - max(0, person.body_fat_pct - 15) * 0.02
        muscle_factor = 1.0 + (person.muscle_mass_kg - 30) * 0.01
        
        # 生活習慣影響
        exercise_factor = 1.0 + person.exercise_frequency * 0.05
        sleep_factor = 1.0 - abs(person.sleep_hours - 7.5) * 0.1
        stress_factor = 1.0 - (person.stress_level - 1) * 0.05
        
        # 疾病影響
        diabetes_factor = {0: 1.0, 1: 0.8, 2: 0.4, 3: 0.6}[person.diabetes_type]
        
        # 藥物影響
        metformin_factor = 1.0 + context.metformin_dose * 0.0001
        
        # 運動後效應
        exercise_boost = 1.2 if context.recent_exercise and context.hours_since_exercise < 2 else 1.0
        
        sensitivity = (base_sensitivity * age_factor * bmi_factor * fat_factor * 
                      muscle_factor * exercise_factor * sleep_factor * stress_factor * 
                      diabetes_factor * metformin_factor * exercise_boost)
        
        return max(0.2, min(2.0, sensitivity))
    
    def calculate_absorption_kinetics(self, food: FoodComposition, gastric_rate: float) -> Dict[str, float]:
        """計算營養素吸收動力學"""
        # 碳水吸收速率
        carb_absorption = gastric_rate * self.carb_type_factors[food.carb_type]
        
        # 考慮顆粒大小
        particle_factor = 2.0 - food.particle_size  # 顆粒越小吸收越快
        carb_absorption *= particle_factor
        
        # 考慮pH值影響
        ph_factor = 1.0 + abs(food.ph_value - 6.5) * 0.05
        carb_absorption *= ph_factor
        
        # 蛋白質和脂肪的延緩效應
        protein_delay = 1.0 - food.protein_g * 0.008
        fat_delay = 1.0 - food.fat_g * 0.012
        
        final_absorption = carb_absorption * protein_delay * fat_delay
        
        return {
            'carb_absorption_rate': final_absorption,
            'peak_time_hours': 2.0 / final_absorption,
            'duration_hours': 4.0 / final_absorption
        }
    
    def calculate_base_glycemic_load(self, food: FoodComposition) -> float:
        """計算基礎血糖負荷（擴展版）"""
        # 原始Lee公式
        base_gl = (self.base_coefficients['intercept'] +
                  self.base_coefficients['available_carb'] * food.net_carbs_g +
                  self.base_coefficients['fat'] * food.fat_g +
                  self.base_coefficients['protein_squared'] * (food.protein_g ** 2) +
                  self.base_coefficients['fiber_squared'] * (food.fiber_g ** 2))
        
        # 新增因子
        # 添加糖的額外影響
        sugar_boost = food.sugar_g * 0.15
        
        # 可溶性vs不可溶性纖維的差異影響
        soluble_fiber_effect = -food.soluble_fiber_g * 0.3
        insoluble_fiber_effect = -food.insoluble_fiber_g * 0.15
        
        # 鈉鉀比例影響（高鈉可能影響胰島素敏感性）
        sodium_potassium_ratio = food.sodium_mg / max(food.potassium_mg, 100)
        electrolyte_effect = sodium_potassium_ratio * 0.5
        
        # 含水量影響（稀釋效應）
        water_dilution = -food.water_content_pct * 0.02
        
        enhanced_gl = (base_gl + sugar_boost + soluble_fiber_effect + 
                      insoluble_fiber_effect + electrolyte_effect + water_dilution)
        
        return max(0, enhanced_gl)
    
    def apply_cooking_modifications(self, base_gl: float, food: FoodComposition) -> float:
        """應用烹調修正（擴展版）"""
        # 基本烹調方式
        cooking_modifier = self.cooking_factors[food.cooking_method]
        
        # 烹調時間和溫度的影響
        time_factor = 1.0 + (food.cooking_time_min - 10) * 0.002
        temp_factor = 1.0 + max(0, food.cooking_temp_c - 100) * 0.001
        
        # 用油量影響
        oil_factor = 1.0 + food.oil_amount_g * 0.01
        
        # 冷藏效應（抗性澱粉形成）
        cooling_factor = 0.75 if food.cooling_effect else 1.0
        
        modified_gl = base_gl * cooking_modifier * time_factor * temp_factor * oil_factor * cooling_factor
        
        return modified_gl
    
    def calculate_meal_interaction_effects(self, current_gl: float, meal_history: MealHistory,
                                         context: MealContext) -> float:
        """計算餐食交互作用效應（修正版）"""
        # 前餐累積效應（降低影響係數）
        cumulative_effect = meal_history.get_cumulative_effect(0)
        interaction_factor = 1.0 + cumulative_effect * 0.01  # 從0.1降到0.01

        # 第二餐效應（Staub-Traugott效應）
        if context.hours_since_last_meal < 2.0:
            second_meal_factor = 0.7  # 很短時間內第二餐升糖更低
        elif context.hours_since_last_meal < 4.0:
            second_meal_factor = 0.85  # 第二餐通常升糖較低
        else:
            second_meal_factor = 1.0

        # 禁食時間的反彈效應（降低影響）
        fasting_rebound = 1.0 + max(0, context.fasting_hours - 12) * 0.01  # 從0.03降到0.01

        # 限制最大疊加效應
        final_gl = current_gl * interaction_factor * second_meal_factor * fasting_rebound
        max_multiplier = 3.0  # 最大不超過原值的3倍
        final_gl = min(final_gl, current_gl * max_multiplier)

        return final_gl

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, person: PersonalProfile,
                                             context: MealContext, meal_history: MealHistory) -> Dict[str, float]:
        """計算綜合升糖基準指數"""

        # 第一層：基礎血糖負荷
        base_gl = self.calculate_base_glycemic_load(food)

        # 第二層：食物加工和烹調修正
        cooking_modified_gl = self.apply_cooking_modifications(base_gl, food)

        # 第三層：個人生理修正
        insulin_sensitivity = self.calculate_insulin_sensitivity_index(person, context)
        gastric_emptying = self.calculate_gastric_emptying_rate(food, person)

        # 代謝修正因子
        metabolic_factor = (2.0 - insulin_sensitivity) * gastric_emptying

        # 晝夜節律修正
        circadian_factor = self.circadian_factors[context.time_of_day]

        # 環境因子（溫度、濕度、海拔）
        temp_factor = 1.0 + (context.ambient_temp_c - 25) * 0.002
        altitude_factor = 1.0 + context.altitude_m * 0.00001

        personal_modified_gl = (cooking_modified_gl * metabolic_factor *
                               circadian_factor * temp_factor * altitude_factor)

        # 第四層：餐食交互作用
        final_gl = self.calculate_meal_interaction_effects(personal_modified_gl, meal_history, context)

        # 第五層：藥物和補充劑修正
        if context.alpha_glucosidase_inhibitor:
            final_gl *= 0.7  # α-葡萄糖苷酶抑制劑效果

        if context.metformin_dose > 0:
            metformin_reduction = min(0.3, context.metformin_dose * 0.0002)
            final_gl *= (1.0 - metformin_reduction)

        # 計算吸收動力學
        absorption_kinetics = self.calculate_absorption_kinetics(food, gastric_emptying)

        # 計算升糖速率和峰值
        peak_time = absorption_kinetics['peak_time_hours']
        glycemic_rate = final_gl / peak_time
        peak_glucose_rise = final_gl * 1.2  # 峰值通常比GL高20%

        # 安全評估
        safety_threshold = self._calculate_dynamic_safety_threshold(person, context)
        risk_level = self._assess_comprehensive_risk(glycemic_rate, safety_threshold, person)

        return {
            'base_gl': base_gl,
            'cooking_modified_gl': cooking_modified_gl,
            'personal_modified_gl': personal_modified_gl,
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'peak_glucose_rise': peak_glucose_rise,
            'peak_time_hours': peak_time,
            'duration_hours': absorption_kinetics['duration_hours'],
            'insulin_sensitivity': insulin_sensitivity,
            'gastric_emptying_rate': gastric_emptying,
            'safety_threshold': safety_threshold,
            'is_safe': glycemic_rate <= safety_threshold,
            'risk_level': risk_level,
            'confidence_score': self._calculate_prediction_confidence(person, food)
        }

    def _calculate_dynamic_safety_threshold(self, person: PersonalProfile, context: MealContext) -> float:
        """計算動態安全閾值"""
        base_threshold = 15.0

        # 個人因子調整
        if person.diabetes_type > 0:
            base_threshold *= 0.7  # 糖尿病患者更嚴格

        if person.bmi > 25:
            base_threshold *= 0.85

        if person.age > 50:
            base_threshold *= 0.9

        # HbA1c影響
        if person.hba1c > 6.5:
            base_threshold *= 0.6
        elif person.hba1c > 5.7:
            base_threshold *= 0.8

        # 時間因子
        if context.time_of_day in [TimeOfDay.NIGHT, TimeOfDay.LATE_NIGHT]:
            base_threshold *= 0.8  # 夜間更嚴格

        # 運動後放寬
        if context.recent_exercise and context.hours_since_exercise < 2:
            base_threshold *= 1.3

        return base_threshold

    def _assess_comprehensive_risk(self, glycemic_rate: float, threshold: float, person: PersonalProfile) -> str:
        """綜合風險評估"""
        ratio = glycemic_rate / threshold

        # 基礎風險等級
        if ratio <= 0.6:
            base_risk = "極低風險"
        elif ratio <= 0.8:
            base_risk = "低風險"
        elif ratio <= 1.0:
            base_risk = "中等風險"
        elif ratio <= 1.3:
            base_risk = "高風險"
        elif ratio <= 1.8:
            base_risk = "極高風險"
        else:
            base_risk = "危險"

        # 個人風險修正
        if person.diabetes_type > 0 and ratio > 0.8:
            risk_levels = ["極低風險", "低風險", "中等風險", "高風險", "極高風險", "危險"]
            current_index = risk_levels.index(base_risk)
            if current_index < len(risk_levels) - 1:
                base_risk = risk_levels[current_index + 1]

        return base_risk

    def _calculate_prediction_confidence(self, person: PersonalProfile, food: FoodComposition) -> float:
        """計算預測置信度"""
        confidence = 0.8  # 基礎置信度

        # 個人數據完整性
        if person.muscle_mass_kg > 0:
            confidence += 0.05
        if person.hba1c > 0:
            confidence += 0.05
        if person.insulin_sensitivity != 1.0:
            confidence += 0.05

        # 食物數據完整性
        if food.soluble_fiber_g > 0:
            confidence += 0.02
        if food.water_content_pct > 0:
            confidence += 0.02
        if food.cooking_time_min > 0:
            confidence += 0.01

        return min(0.95, confidence)

    def calculate_intervention_strategy(self, food: FoodComposition, person: PersonalProfile,
                                      context: MealContext, meal_history: MealHistory,
                                      target_reduction: float = 0.4) -> Dict[str, any]:
        """計算綜合營養干預策略"""

        current_result = self.calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        current_rate = current_result['glycemic_rate']
        target_rate = current_rate * (1 - target_reduction)

        interventions = {}

        # 纖維干預（多種類型）
        soluble_fiber_need = self._calculate_soluble_fiber_needs(current_rate, target_rate)
        insoluble_fiber_need = self._calculate_insoluble_fiber_needs(current_rate, target_rate)

        # 蛋白質干預（考慮類型）
        whey_protein_need = self._calculate_whey_protein_needs(current_rate, target_rate)
        casein_protein_need = self._calculate_casein_protein_needs(current_rate, target_rate)

        # 脂肪干預（健康脂肪）
        omega3_need = self._calculate_omega3_needs(current_rate, target_rate)
        mct_oil_need = self._calculate_mct_needs(current_rate, target_rate)

        # 功能性成分
        chromium_need = self._calculate_chromium_needs(current_rate, target_rate)
        cinnamon_need = self._calculate_cinnamon_needs(current_rate, target_rate)

        # 時機干預
        timing_strategy = self._calculate_timing_strategy(current_result, context)

        # 運動干預
        exercise_strategy = self._calculate_exercise_intervention(current_rate, target_rate, person)

        return {
            'current_glycemic_rate': current_rate,
            'target_glycemic_rate': target_rate,
            'fiber_interventions': {
                'soluble_fiber_g': soluble_fiber_need,
                'insoluble_fiber_g': insoluble_fiber_need,
                'recommended_sources': ['燕麥', '蘋果果膠', '洋車前子殼']
            },
            'protein_interventions': {
                'whey_protein_g': whey_protein_need,
                'casein_protein_g': casein_protein_need,
                'recommended_timing': '餐前30分鐘'
            },
            'fat_interventions': {
                'omega3_g': omega3_need,
                'mct_oil_ml': mct_oil_need,
                'recommended_sources': ['魚油', '亞麻籽油', '椰子油']
            },
            'functional_supplements': {
                'chromium_mcg': chromium_need,
                'cinnamon_g': cinnamon_need,
                'alpha_lipoic_acid_mg': 200 if current_rate > target_rate * 1.5 else 0
            },
            'timing_strategy': timing_strategy,
            'exercise_strategy': exercise_strategy,
            'expected_reduction_pct': target_reduction * 100,
            'confidence_level': current_result['confidence_score']
        }

    def _calculate_soluble_fiber_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 1.2)  # 可溶性纖維效果更強

    def _calculate_insoluble_fiber_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 0.6)

    def _calculate_whey_protein_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 0.4)  # 乳清蛋白快速作用

    def _calculate_casein_protein_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 0.3)  # 酪蛋白緩慢釋放

    def _calculate_omega3_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 0.5)

    def _calculate_mct_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, rate_diff / 0.3)

    def _calculate_chromium_needs(self, current_rate: float, target_rate: float) -> float:
        if current_rate > target_rate * 1.2:
            return 200.0  # 微克
        return 0.0

    def _calculate_cinnamon_needs(self, current_rate: float, target_rate: float) -> float:
        rate_diff = current_rate - target_rate
        return max(0, min(6.0, rate_diff * 2))  # 最多6克

    def _calculate_timing_strategy(self, result: Dict, context: MealContext) -> Dict[str, str]:
        peak_time = result['peak_time_hours']

        if peak_time < 1.0:
            return {
                'pre_meal_prep': '餐前45分鐘攝取纖維和蛋白質',
                'during_meal': '細嚼慢嚥，延長用餐時間至30分鐘',
                'post_meal': '餐後立即輕度活動10-15分鐘'
            }
        else:
            return {
                'pre_meal_prep': '餐前30分鐘攝取少量蛋白質',
                'during_meal': '正常用餐節奏',
                'post_meal': '餐後30分鐘開始輕度活動'
            }

    def _calculate_exercise_intervention(self, current_rate: float, target_rate: float,
                                       person: PersonalProfile) -> Dict[str, any]:
        rate_diff = current_rate - target_rate

        if rate_diff <= 0:
            return {'type': 'none', 'duration': 0}

        # 根據個人體能狀況調整
        fitness_level = person.exercise_frequency / 7.0  # 0-1

        if rate_diff < 5:
            return {
                'type': '輕度步行',
                'duration_min': 10 + rate_diff * 2,
                'intensity': '低強度',
                'timing': '餐後15-30分鐘開始'
            }
        elif rate_diff < 15:
            return {
                'type': '快走或慢跑',
                'duration_min': 15 + rate_diff * 1.5,
                'intensity': '中低強度',
                'timing': '餐後30分鐘開始'
            }
        else:
            return {
                'type': '間歇性運動',
                'duration_min': 20 + min(rate_diff, 30),
                'intensity': '中等強度',
                'timing': '餐後45分鐘開始',
                'pattern': '2分鐘活動 + 1分鐘休息'
            }
