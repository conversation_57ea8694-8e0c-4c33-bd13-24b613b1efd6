"""
平衡精準升糖基準指數公式
目標：找到預測值和實際值之間的平衡點，達到10%以下誤差

問題分析：
- 原始公式預測值過高（65.7%誤差）
- 超精準公式預測值過低（99.8%誤差）
- 需要找到中間平衡點
"""

from practical_formula_series import PracticalFormulaA1, PracticalPersonalProfile
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, CarbType
from typing import Dict

class BalancedPreciseFormula(PracticalFormulaA1):
    """平衡精準公式 - 尋找最佳平衡點"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "平衡精準版"
        self.version = "Balanced"
        
        # 平衡的校準係數（基於實測數據反向推算）
        # 目標：實際值約100mg/dL，需要找到合適的係數
        self.balanced_calibration = {
            'base_multiplier': 1.2,      # 適中的基礎係數
            'carb_efficiency': 0.8,      # 碳水效率
            'personal_impact': 0.9,      # 個人因子影響
            'lifestyle_impact': 0.85,    # 生活習慣影響
        }
        
        # 平衡的轉換係數（從GL到mg/dL）
        self.balanced_conversion = 1.5  # 適中的轉換係數
        
        # 學習參數
        self.learning_rate = 0.1
        self.target_range = (90, 110)  # 目標範圍
    
    def calculate_balanced_base_gl(self, food: FoodComposition) -> float:
        """計算平衡的基礎GL"""
        # 使用適中的GI值
        moderate_gi = {
            CarbType.SIMPLE_SUGAR: 60,      # 適中值
            CarbType.DISACCHARIDE: 55,      
            CarbType.STARCH: 50,            
            CarbType.COMPLEX_CARB: 40,      
            CarbType.RESISTANT_STARCH: 25   
        }
        
        base_gi = moderate_gi.get(food.carb_type, 45)
        
        # 適度的營養素調整
        fiber_factor = max(0.6, 1.0 - food.fiber_g * 0.04)
        protein_factor = max(0.7, 1.0 - food.protein_g * 0.02)
        fat_factor = max(0.6, 1.0 - food.fat_g * 0.025)
        
        # 加工程度適度影響
        processing_factor = 1.0 + (food.processing_level.value - 2) * 0.1
        
        # 計算平衡GL
        adjusted_gi = base_gi * fiber_factor * protein_factor * fat_factor * processing_factor
        balanced_gl = (adjusted_gi * food.total_carbs_g * self.balanced_calibration['carb_efficiency']) / 100
        
        return max(5.0, balanced_gl)  # 最小值5.0
    
    def calculate_balanced_personal_factor(self, person: PracticalPersonalProfile) -> float:
        """計算平衡的個人因子"""
        factor = 1.0
        
        # 適度的HbA1c影響
        if person.hba1c > 8.0:
            hba1c_factor = 1.0 + (person.hba1c - 8.0) * 0.15
        elif person.hba1c > 7.0:
            hba1c_factor = 1.0 + (person.hba1c - 7.0) * 0.1
        else:
            hba1c_factor = 1.0
        
        factor *= hba1c_factor
        
        # 適度的BMI影響
        if person.bmi > 30:
            bmi_factor = 1.0 + (person.bmi - 30) * 0.04
        elif person.bmi > 25:
            bmi_factor = 1.0 + (person.bmi - 25) * 0.02
        else:
            bmi_factor = 1.0
        
        factor *= bmi_factor
        
        # 年齡影響
        if person.age > 60:
            age_factor = 1.0 + (person.age - 60) * 0.01
        else:
            age_factor = 1.0
        
        factor *= age_factor
        
        return factor * self.balanced_calibration['personal_impact']
    
    def calculate_balanced_lifestyle_factor(self, person: PracticalPersonalProfile) -> float:
        """計算平衡的生活習慣因子"""
        factor = 1.0
        
        # 適度的運動影響
        if person.exercise_frequency >= 4:
            factor *= 0.8   # 經常運動適度保護
        elif person.exercise_frequency >= 2:
            factor *= 0.9   # 適度運動輕微保護
        elif person.exercise_frequency == 0:
            factor *= 1.2   # 不運動適度影響
        
        # 適度的睡眠影響
        if person.sleep_hours < 6:
            factor *= 1.15  # 睡眠不足適度影響
        elif person.sleep_hours > 8:
            factor *= 0.95  # 充足睡眠輕微保護
        
        # 適度的壓力影響
        if person.stress_level >= 4:
            factor *= 1.1   # 高壓力適度影響
        elif person.stress_level <= 2:
            factor *= 0.95  # 低壓力輕微保護
        
        # 血壓和心率的適度影響
        if person.blood_pressure_sys > 140:
            factor *= 1.05
        if person.resting_hr > 80:
            factor *= 1.03
        elif person.resting_hr < 60:
            factor *= 0.97
        
        return factor * self.balanced_calibration['lifestyle_impact']
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: PracticalPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 計算平衡的各個因子
        balanced_base_gl = self.calculate_balanced_base_gl(food)
        personal_factor = self.calculate_balanced_personal_factor(person)
        lifestyle_factor = self.calculate_balanced_lifestyle_factor(person)
        
        # 餐食情境的適度調整
        context_factor = 1.0
        if context.fasting_hours > 8:
            context_factor *= 1.08
        elif context.fasting_hours < 3:
            context_factor *= 0.95
        
        # 計算最終GL
        final_gl = (balanced_base_gl * 
                   personal_factor * 
                   lifestyle_factor * 
                   context_factor * 
                   self.balanced_calibration['base_multiplier'])
        
        # 計算峰值時間
        peak_time = self._estimate_peak_time(food, person)
        
        # 計算升糖速率
        glycemic_rate = final_gl / peak_time
        
        # 使用平衡的轉換係數
        predicted_ppg_change = final_gl * self.balanced_conversion
        
        # 安全評估
        safety_threshold = self._calculate_practical_safety_threshold(person)
        is_safe = glycemic_rate <= safety_threshold
        risk_level = self._assess_practical_risk(glycemic_rate, safety_threshold, person)
        
        return {
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'peak_time_hours': peak_time,
            'predicted_ppg_change': predicted_ppg_change,
            'is_safe': is_safe,
            'risk_level': risk_level,
            'safety_threshold': safety_threshold,
            'confidence_score': 0.85,
            'balanced_base_gl': balanced_base_gl,
            'personal_factor': personal_factor,
            'lifestyle_factor': lifestyle_factor,
            'context_factor': context_factor,
            'base_multiplier': self.balanced_calibration['base_multiplier']
        }
    
    def smart_calibrate(self, predicted_ppg: float, actual_ppg: float):
        """智能校準 - 基於誤差方向調整"""
        error_ratio = predicted_ppg / max(actual_ppg, 1.0)
        error_magnitude = abs(error_ratio - 1.0)
        
        # 根據誤差大小決定調整幅度
        if error_magnitude > 0.5:  # 誤差>50%
            adjustment_rate = self.learning_rate * 2
        elif error_magnitude > 0.2:  # 誤差>20%
            adjustment_rate = self.learning_rate * 1.5
        else:  # 誤差≤20%
            adjustment_rate = self.learning_rate * 0.5
        
        # 調整係數
        if error_ratio > 1.1:  # 預測偏高
            self.balanced_calibration['base_multiplier'] *= (1 - adjustment_rate)
            self.balanced_conversion *= (1 - adjustment_rate)
        elif error_ratio < 0.9:  # 預測偏低
            self.balanced_calibration['base_multiplier'] *= (1 + adjustment_rate)
            self.balanced_conversion *= (1 + adjustment_rate)
        
        # 限制係數範圍
        self.balanced_calibration['base_multiplier'] = max(0.3, min(3.0, self.balanced_calibration['base_multiplier']))
        self.balanced_conversion = max(0.5, min(5.0, self.balanced_conversion))
        
        # 微調其他係數
        if actual_ppg > 110:  # 實際值偏高，可能需要增強個人因子影響
            self.balanced_calibration['personal_impact'] *= 1.02
        elif actual_ppg < 90:  # 實際值偏低，可能需要降低個人因子影響
            self.balanced_calibration['personal_impact'] *= 0.98
        
        # 限制其他係數範圍
        for key in ['carb_efficiency', 'personal_impact', 'lifestyle_impact']:
            self.balanced_calibration[key] = max(0.5, min(1.5, self.balanced_calibration[key]))
    
    def get_calibration_status(self) -> Dict:
        """獲取校準狀態"""
        return {
            'balanced_calibration': self.balanced_calibration.copy(),
            'balanced_conversion': self.balanced_conversion
        }
    
    def is_in_target_range(self, predicted_ppg: float, actual_ppg: float) -> bool:
        """檢查是否在目標範圍內"""
        error_pct = abs(predicted_ppg - actual_ppg) / actual_ppg * 100
        return error_pct <= 10.0
