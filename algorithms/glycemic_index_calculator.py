"""
升糖基準指數計算器
基於多項科學研究的血糖升糖速率預測算法

主要參考文獻:
1. <PERSON> et al. (2021) - GL prediction formula for ready-to-eat meals
2. International standards for glycemic index measurement
3. Various studies on factors affecting postprandial glucose response
"""

import math
from typing import Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class CarbType(Enum):
    """碳水化合物類型"""
    SIMPLE_SUGAR = "simple_sugar"  # 單醣
    STARCH = "starch"              # 澱粉
    COMPLEX_CARB = "complex_carb"  # 複合碳水

class ProcessingLevel(Enum):
    """食物加工等級"""
    RAW = 1          # 生食
    MINIMAL = 2      # 輕度加工
    MODERATE = 3     # 中度加工
    HIGHLY = 4       # 高度加工

class CookingMethod(Enum):
    """烹調方式"""
    RAW = "raw"
    BOILED = "boiled"
    STEAMED = "steamed"
    BAKED = "baked"
    FRIED = "fried"
    GRILLED = "grilled"

class TimeOfDay(Enum):
    """進食時間"""
    MORNING = "morning"    # 早晨 (6-10)
    MIDDAY = "midday"      # 中午 (11-14)
    AFTERNOON = "afternoon" # 下午 (15-17)
    EVENING = "evening"    # 晚上 (18-21)
    NIGHT = "night"        # 夜間 (22-5)

@dataclass
class FoodComposition:
    """食物營養成分"""
    carb_type: CarbType
    total_carbs_g: float
    fiber_g: float
    protein_g: float
    fat_g: float
    processing_level: ProcessingLevel
    cooking_method: CookingMethod
    cooling_effect: bool = False  # 是否經過冷藏（影響抗性澱粉）
    
    @property
    def available_carbs_g(self) -> float:
        """可用碳水化合物（總碳水 - 纖維）"""
        return max(0, self.total_carbs_g - self.fiber_g)

@dataclass
class PersonalProfile:
    """個人生理參數"""
    age: int
    weight_kg: float
    bmi: float
    body_fat_pct: float
    
    @property
    def metabolic_factor(self) -> float:
        """代謝因子（基於年齡和體組成）"""
        # 年齡因子：年齡越大，胰島素敏感性越低
        age_factor = 1.0 + (self.age - 25) * 0.005
        
        # BMI因子：BMI越高，胰島素阻抗越強
        bmi_factor = 1.0 + max(0, self.bmi - 23) * 0.02
        
        # 體脂因子：體脂率越高，胰島素敏感性越低
        fat_factor = 1.0 + max(0, self.body_fat_pct - 15) * 0.01
        
        return age_factor * bmi_factor * fat_factor

@dataclass
class MealContext:
    """進餐情境"""
    time_of_day: TimeOfDay
    fasting_hours: float = 8.0  # 禁食小時數
    
    @property
    def circadian_factor(self) -> float:
        """晝夜節律因子"""
        factors = {
            TimeOfDay.MORNING: 1.2,    # 晨間胰島素敏感性較高
            TimeOfDay.MIDDAY: 1.0,     # 基準值
            TimeOfDay.AFTERNOON: 0.9,  # 下午略低
            TimeOfDay.EVENING: 0.8,    # 晚間較低
            TimeOfDay.NIGHT: 0.7       # 夜間最低
        }
        return factors.get(self.time_of_day, 1.0)

class GlycemicIndexCalculator:
    """升糖基準指數計算器"""
    
    def __init__(self):
        # 基於Lee et al. (2021)的基礎公式係數
        self.base_coefficients = {
            'intercept': 19.27,
            'available_carb': 0.39,
            'fat': -0.21,
            'protein_squared': -0.01,
            'fiber_squared': -0.01
        }
        
        # 碳水類型修正係數
        self.carb_type_factors = {
            CarbType.SIMPLE_SUGAR: 1.3,    # 單醣升糖更快
            CarbType.STARCH: 1.0,          # 基準值
            CarbType.COMPLEX_CARB: 0.8     # 複合碳水升糖較慢
        }
        
        # 加工等級修正係數
        self.processing_factors = {
            ProcessingLevel.RAW: 0.8,
            ProcessingLevel.MINIMAL: 0.9,
            ProcessingLevel.MODERATE: 1.0,
            ProcessingLevel.HIGHLY: 1.2
        }
        
        # 烹調方式修正係數
        self.cooking_factors = {
            CookingMethod.RAW: 0.8,
            CookingMethod.STEAMED: 0.9,
            CookingMethod.BOILED: 1.0,
            CookingMethod.BAKED: 1.1,
            CookingMethod.GRILLED: 1.1,
            CookingMethod.FRIED: 1.3
        }
    
    def calculate_base_gl(self, food: FoodComposition) -> float:
        """計算基礎血糖負荷（基於Lee et al.公式）"""
        gl = (self.base_coefficients['intercept'] +
              self.base_coefficients['available_carb'] * food.available_carbs_g +
              self.base_coefficients['fat'] * food.fat_g +
              self.base_coefficients['protein_squared'] * (food.protein_g ** 2) +
              self.base_coefficients['fiber_squared'] * (food.fiber_g ** 2))
        
        return max(0, gl)  # 確保非負值
    
    def apply_food_modifiers(self, base_gl: float, food: FoodComposition) -> float:
        """應用食物特性修正因子"""
        # 碳水類型修正
        carb_modifier = self.carb_type_factors.get(food.carb_type, 1.0)
        
        # 加工等級修正
        processing_modifier = self.processing_factors.get(food.processing_level, 1.0)
        
        # 烹調方式修正
        cooking_modifier = self.cooking_factors.get(food.cooking_method, 1.0)
        
        # 冷藏效應（增加抗性澱粉）
        cooling_modifier = 0.85 if food.cooling_effect else 1.0
        
        modified_gl = base_gl * carb_modifier * processing_modifier * cooking_modifier * cooling_modifier
        
        return modified_gl
    
    def apply_personal_modifiers(self, gl: float, person: PersonalProfile, context: MealContext) -> float:
        """應用個人化修正因子"""
        # 個人代謝因子
        metabolic_modifier = person.metabolic_factor
        
        # 晝夜節律因子
        circadian_modifier = context.circadian_factor
        
        # 禁食時間因子（禁食時間越長，升糖反應越強）
        fasting_modifier = 1.0 + max(0, context.fasting_hours - 8) * 0.02
        
        personalized_gl = gl * metabolic_modifier * circadian_modifier * fasting_modifier
        
        return personalized_gl
    
    def calculate_glycemic_index(self, food: FoodComposition, person: PersonalProfile, 
                               context: MealContext) -> Dict[str, float]:
        """計算完整的升糖基準指數"""
        
        # 步驟1: 計算基礎GL
        base_gl = self.calculate_base_gl(food)
        
        # 步驟2: 應用食物修正因子
        food_modified_gl = self.apply_food_modifiers(base_gl, food)
        
        # 步驟3: 應用個人化修正因子
        final_gl = self.apply_personal_modifiers(food_modified_gl, person, context)
        
        # 計算升糖速率（GL/時間）
        glycemic_rate = final_gl / 2.0  # 假設2小時內達到峰值
        
        # 計算安全閾值（基於個人參數）
        safety_threshold = self._calculate_safety_threshold(person)
        
        return {
            'base_gl': base_gl,
            'food_modified_gl': food_modified_gl,
            'final_gl': final_gl,
            'glycemic_rate': glycemic_rate,
            'safety_threshold': safety_threshold,
            'is_safe': glycemic_rate <= safety_threshold,
            'risk_level': self._assess_risk_level(glycemic_rate, safety_threshold)
        }
    
    def _calculate_safety_threshold(self, person: PersonalProfile) -> float:
        """計算個人安全閾值"""
        # 基礎安全閾值
        base_threshold = 15.0
        
        # 根據個人參數調整
        if person.bmi > 25:
            base_threshold *= 0.8  # 超重者需要更嚴格控制
        if person.age > 50:
            base_threshold *= 0.9  # 年長者需要更謹慎
        
        return base_threshold
    
    def _assess_risk_level(self, glycemic_rate: float, threshold: float) -> str:
        """評估風險等級"""
        ratio = glycemic_rate / threshold
        
        if ratio <= 0.7:
            return "低風險"
        elif ratio <= 1.0:
            return "中等風險"
        elif ratio <= 1.5:
            return "高風險"
        else:
            return "極高風險"
    
    def calculate_intervention_needs(self, food: FoodComposition, person: PersonalProfile,
                                   context: MealContext, target_reduction: float = 0.3) -> Dict[str, float]:
        """計算營養干預需求"""
        
        current_result = self.calculate_glycemic_index(food, person, context)
        current_rate = current_result['glycemic_rate']
        target_rate = current_rate * (1 - target_reduction)
        
        # 計算需要的額外纖維
        additional_fiber = self._calculate_fiber_needs(current_rate, target_rate)
        
        # 計算需要的額外蛋白質
        additional_protein = self._calculate_protein_needs(current_rate, target_rate)
        
        # 計算需要的健康脂肪
        additional_fat = self._calculate_fat_needs(current_rate, target_rate)
        
        return {
            'current_glycemic_rate': current_rate,
            'target_glycemic_rate': target_rate,
            'additional_fiber_g': additional_fiber,
            'additional_protein_g': additional_protein,
            'additional_healthy_fat_g': additional_fat,
            'intervention_effectiveness': target_reduction
        }
    
    def _calculate_fiber_needs(self, current_rate: float, target_rate: float) -> float:
        """計算所需額外纖維量"""
        rate_difference = current_rate - target_rate
        # 基於研究，每克纖維約可降低GL 0.5-1.0
        fiber_efficiency = 0.75
        return max(0, rate_difference / fiber_efficiency)
    
    def _calculate_protein_needs(self, current_rate: float, target_rate: float) -> float:
        """計算所需額外蛋白質量"""
        rate_difference = current_rate - target_rate
        # 蛋白質的升糖抑制效果較溫和
        protein_efficiency = 0.3
        return max(0, rate_difference / protein_efficiency)
    
    def _calculate_fat_needs(self, current_rate: float, target_rate: float) -> float:
        """計算所需健康脂肪量"""
        rate_difference = current_rate - target_rate
        # 健康脂肪可延緩胃排空，降低升糖速度
        fat_efficiency = 0.4
        return max(0, rate_difference / fat_efficiency)
