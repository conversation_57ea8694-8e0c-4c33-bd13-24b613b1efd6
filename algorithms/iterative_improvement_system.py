"""
迭代改進系統
目標：持續優化直到90%人群達到10%以下誤差
策略：深度分析 + 精準調整 + 持續迭代
"""

from final_breakthrough_formula import FinalBreakthroughFormula
from formula_validation_test import ClinicalDataValidator
from test_practical_formulas import create_practical_test_person
from advanced_glycemic_calculator import FoodComposition, MealContext, MealHistory, TimeOfDay, CarbType, ProcessingLevel, CookingMethod
import math
import random

def np_mean(data):
    return sum(data) / len(data) if data else 0

def np_std(data):
    if not data:
        return 0
    mean = np_mean(data)
    variance = sum((x - mean) ** 2 for x in data) / len(data)
    return variance ** 0.5

class IterativeImprovementSystem(FinalBreakthroughFormula):
    """迭代改進系統"""
    
    def __init__(self):
        super().__init__()
        self.iteration_count = 0
        self.best_accuracy = 0.0
        self.best_params = None
        self.error_analysis = []
        
        # 動態調整參數
        self.dynamic_params = {
            'base_multiplier': 2.022,      # 從成功基礎開始
            'conversion_factor': 2.621,   # 從成功基礎開始
            'hba1c_sensitivity': 0.12,    # HbA1c敏感度
            'bmi_sensitivity': 0.03,      # BMI敏感度
            'age_sensitivity': 0.008,     # 年齡敏感度
            'exercise_protection': 0.15,  # 運動保護
            'fiber_effectiveness': 0.04,  # 纖維效果
            'protein_effectiveness': 0.02, # 蛋白質效果
            'fat_effectiveness': 0.025,   # 脂肪效果
            'individual_variance': 0.92   # 個體差異
        }
        
        # 學習參數
        self.learning_config = {
            'learning_rate': 0.05,
            'momentum': 0.9,
            'adaptive_rate': True,
            'target_accuracy': 0.90
        }
        
        # 參數變化歷史
        self.param_history = []
        self.momentum_values = {}
    
    def analyze_error_patterns(self, errors: list, patient_data: list) -> dict:
        """分析誤差模式"""
        analysis = {
            'high_error_patients': [],
            'low_error_patients': [],
            'error_by_hba1c': {},
            'error_by_bmi': {},
            'error_by_age': {},
            'patterns': []
        }
        
        for i, error in enumerate(errors):
            patient = patient_data[i]
            
            if error > 15.0:  # 高誤差患者
                analysis['high_error_patients'].append({
                    'index': i,
                    'error': error,
                    'hba1c': patient['hba1c'],
                    'bmi': patient['bmi'],
                    'age': patient['age']
                })
            elif error <= 5.0:  # 低誤差患者
                analysis['low_error_patients'].append({
                    'index': i,
                    'error': error,
                    'hba1c': patient['hba1c'],
                    'bmi': patient['bmi'],
                    'age': patient['age']
                })
        
        # 按HbA1c分組分析
        hba1c_groups = {'<6.0': [], '6.0-7.0': [], '7.0-8.0': [], '>8.0': []}
        for i, error in enumerate(errors):
            hba1c = patient_data[i]['hba1c']
            if hba1c < 6.0:
                hba1c_groups['<6.0'].append(error)
            elif hba1c < 7.0:
                hba1c_groups['6.0-7.0'].append(error)
            elif hba1c < 8.0:
                hba1c_groups['7.0-8.0'].append(error)
            else:
                hba1c_groups['>8.0'].append(error)
        
        for group, group_errors in hba1c_groups.items():
            if group_errors:
                analysis['error_by_hba1c'][group] = {
                    'avg_error': np_mean(group_errors),
                    'accuracy_rate': sum(1 for e in group_errors if e <= 10.0) / len(group_errors) * 100
                }
        
        return analysis
    
    def adaptive_parameter_update(self, errors: list, patient_data: list):
        """自適應參數更新"""
        analysis = self.analyze_error_patterns(errors, patient_data)
        
        # 計算當前準確率
        current_accuracy = sum(1 for e in errors if e <= 10.0) / len(errors)
        
        # 動態調整學習率
        if current_accuracy > self.best_accuracy:
            self.learning_config['learning_rate'] *= 1.1  # 增加學習率
        else:
            self.learning_config['learning_rate'] *= 0.9  # 減少學習率
        
        # 限制學習率範圍
        self.learning_config['learning_rate'] = max(0.01, min(0.2, self.learning_config['learning_rate']))
        
        # 基於誤差模式調整參數
        avg_error = np_mean(errors)
        
        # 如果平均誤差過高，調整基礎係數
        if avg_error > 15.0:
            self._adjust_with_momentum('base_multiplier', -0.1)
            self._adjust_with_momentum('conversion_factor', -0.1)
        elif avg_error < 5.0:
            self._adjust_with_momentum('base_multiplier', 0.05)
            self._adjust_with_momentum('conversion_factor', 0.05)
        
        # 基於HbA1c組別表現調整
        if 'error_by_hba1c' in analysis:
            for group, stats in analysis['error_by_hba1c'].items():
                if stats['accuracy_rate'] < 50:  # 該組準確率低
                    if group == '>8.0':
                        self._adjust_with_momentum('hba1c_sensitivity', 0.02)
                    elif group == '<6.0':
                        self._adjust_with_momentum('hba1c_sensitivity', -0.01)
        
        # 基於高誤差患者特徵調整
        if analysis['high_error_patients']:
            high_error_hba1c = np_mean([p['hba1c'] for p in analysis['high_error_patients']])
            high_error_bmi = np_mean([p['bmi'] for p in analysis['high_error_patients']])
            
            if high_error_hba1c > 7.5:
                self._adjust_with_momentum('hba1c_sensitivity', 0.01)
            if high_error_bmi > 28:
                self._adjust_with_momentum('bmi_sensitivity', 0.005)
        
        # 記錄參數變化
        self.param_history.append(self.dynamic_params.copy())
    
    def _adjust_with_momentum(self, param_name: str, adjustment: float):
        """使用動量的參數調整"""
        if param_name not in self.momentum_values:
            self.momentum_values[param_name] = 0.0
        
        # 計算動量調整
        momentum = self.learning_config['momentum']
        learning_rate = self.learning_config['learning_rate']
        
        self.momentum_values[param_name] = (momentum * self.momentum_values[param_name] + 
                                          learning_rate * adjustment)
        
        # 應用調整
        self.dynamic_params[param_name] += self.momentum_values[param_name]
        
        # 限制參數範圍
        param_limits = {
            'base_multiplier': (0.5, 5.0),
            'conversion_factor': (1.0, 6.0),
            'hba1c_sensitivity': (0.05, 0.3),
            'bmi_sensitivity': (0.01, 0.1),
            'age_sensitivity': (0.002, 0.02),
            'exercise_protection': (0.05, 0.3),
            'fiber_effectiveness': (0.02, 0.1),
            'protein_effectiveness': (0.01, 0.05),
            'fat_effectiveness': (0.01, 0.05),
            'individual_variance': (0.7, 1.2)
        }
        
        if param_name in param_limits:
            min_val, max_val = param_limits[param_name]
            self.dynamic_params[param_name] = max(min_val, min(max_val, self.dynamic_params[param_name]))
    
    def calculate_improved_personal_factor(self, person_profile) -> float:
        """計算改進的個人因子"""
        factor = 1.0
        
        # 動態HbA1c影響
        if person_profile.hba1c > 8.0:
            hba1c_impact = 1.0 + (person_profile.hba1c - 8.0) * self.dynamic_params['hba1c_sensitivity']
        elif person_profile.hba1c > 7.0:
            hba1c_impact = 1.0 + (person_profile.hba1c - 7.0) * self.dynamic_params['hba1c_sensitivity'] * 0.7
        elif person_profile.hba1c > 6.0:
            hba1c_impact = 1.0 + (person_profile.hba1c - 6.0) * self.dynamic_params['hba1c_sensitivity'] * 0.4
        else:
            hba1c_impact = 1.0
        
        factor *= hba1c_impact
        
        # 動態BMI影響
        bmi = person_profile.weight_kg / (person_profile.height_cm / 100) ** 2
        if bmi > 30:
            bmi_impact = 1.0 + (bmi - 30) * self.dynamic_params['bmi_sensitivity']
        elif bmi > 25:
            bmi_impact = 1.0 + (bmi - 25) * self.dynamic_params['bmi_sensitivity'] * 0.6
        else:
            bmi_impact = 1.0
        
        factor *= bmi_impact
        
        # 動態年齡影響
        if person_profile.age > 60:
            age_impact = 1.0 + (person_profile.age - 60) * self.dynamic_params['age_sensitivity']
        elif person_profile.age > 50:
            age_impact = 1.0 + (person_profile.age - 50) * self.dynamic_params['age_sensitivity'] * 0.5
        else:
            age_impact = 1.0
        
        factor *= age_impact
        
        # 動態運動保護
        if person_profile.exercise_frequency >= 5:
            exercise_impact = 1.0 - self.dynamic_params['exercise_protection']
        elif person_profile.exercise_frequency >= 3:
            exercise_impact = 1.0 - self.dynamic_params['exercise_protection'] * 0.7
        elif person_profile.exercise_frequency >= 1:
            exercise_impact = 1.0 - self.dynamic_params['exercise_protection'] * 0.3
        else:
            exercise_impact = 1.0 + self.dynamic_params['exercise_protection'] * 0.5
        
        factor *= exercise_impact
        
        return factor * self.dynamic_params['individual_variance']
    
    def calculate_improved_gl(self, food: FoodComposition, person_profile) -> float:
        """計算改進的GL"""
        # 動態營養素調整
        fiber_factor = max(0.3, 1.0 - food.fiber_g * self.dynamic_params['fiber_effectiveness'])
        protein_factor = max(0.4, 1.0 - food.protein_g * self.dynamic_params['protein_effectiveness'])
        fat_factor = max(0.3, 1.0 - food.fat_g * self.dynamic_params['fat_effectiveness'])
        
        # 基礎GL計算
        base_gl = food.total_carbs_g * 0.7
        
        # 應用營養素調整
        adjusted_gl = base_gl * fiber_factor * protein_factor * fat_factor
        
        # 應用個人因子
        personal_factor = self.calculate_improved_personal_factor(person_profile)
        
        # 最終GL
        final_gl = adjusted_gl * personal_factor * self.dynamic_params['base_multiplier']
        
        return final_gl
    
    def predict_improved_bg_change(self, food: FoodComposition, person_profile) -> float:
        """預測改進的血糖變化"""
        gl = self.calculate_improved_gl(food, person_profile)
        bg_change = gl * self.dynamic_params['conversion_factor']
        return bg_change
    
    def run_iteration_test(self, validator, iteration: int) -> dict:
        """運行迭代測試"""
        print(f"\n=== 第 {iteration} 次迭代改進 ===")
        
        errors = []
        predictions = []
        actuals = []
        
        # 標準測試餐
        standard_meal = FoodComposition(
            carb_type=CarbType.STARCH,
            total_carbs_g=78.0,
            fiber_g=3.0,
            protein_g=15.0,
            fat_g=8.0,
            processing_level=ProcessingLevel.MODERATE,
            cooking_method=CookingMethod.BOILED,
            sugar_g=5.0,
            starch_g=73.0
        )
        
        for patient_data in validator.getgoal_patients:
            person = create_practical_test_person(patient_data)
            
            # 使用改進算法預測
            predicted_bg = self.predict_improved_bg_change(standard_meal, person)
            actual_bg = abs(patient_data['actual_ppg_change'])
            
            error = abs(predicted_bg - actual_bg) / actual_bg * 100
            errors.append(error)
            predictions.append(predicted_bg)
            actuals.append(actual_bg)
        
        # 計算統計
        avg_error = np_mean(errors)
        accuracy_rate = sum(1 for e in errors if e <= 10.0) / len(errors) * 100
        
        print(f"平均誤差: {avg_error:.1f}%")
        print(f"準確率: {accuracy_rate:.1f}%")
        print(f"學習率: {self.learning_config['learning_rate']:.3f}")
        
        # 更新最佳結果
        if accuracy_rate > self.best_accuracy:
            self.best_accuracy = accuracy_rate
            self.best_params = self.dynamic_params.copy()
            print(f"🎯 新的最佳準確率: {accuracy_rate:.1f}%")
        
        # 自適應參數更新
        self.adaptive_parameter_update(errors, validator.getgoal_patients)
        
        result = {
            'iteration': iteration,
            'avg_error': avg_error,
            'accuracy_rate': accuracy_rate,
            'errors': errors,
            'predictions': predictions,
            'actuals': actuals,
            'params': self.dynamic_params.copy()
        }
        
        self.error_analysis.append(result)
        return result
    
    def get_improvement_summary(self) -> dict:
        """獲取改進總結"""
        if not self.error_analysis:
            return {}
        
        initial = self.error_analysis[0]
        final = self.error_analysis[-1]
        
        return {
            'total_iterations': len(self.error_analysis),
            'initial_accuracy': initial['accuracy_rate'],
            'final_accuracy': final['accuracy_rate'],
            'best_accuracy': self.best_accuracy,
            'improvement': final['accuracy_rate'] - initial['accuracy_rate'],
            'best_params': self.best_params,
            'final_params': final['params']
        }
