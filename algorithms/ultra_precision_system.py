"""
超精準升糖系統
目標：90%人群達到10%以下誤差
策略：激進優化 + 深度學習校準 + 多重驗證
"""

from multi_meal_intervention_system import *
import math

class UltraPrecisionGlycemicSystem(MultiMealGlycemicSystem):
    """超精準升糖系統"""
    
    def __init__(self):
        super().__init__()
        
        # 激進優化的校準係數
        self.ultra_calibration = {
            'base_multiplier': 0.45,      # 大幅降低基礎係數
            'conversion_factor': 1.2,     # 降低GL到血糖轉換
            'individual_precision': 1.8,  # 增強個體精度
            'intervention_amplifier': 2.5, # 放大干預效果
            'multi_meal_damping': 0.3,    # 大幅降低多餐效應
            'safety_margin': 0.85         # 安全邊際
        }
        
        # 深度學習參數
        self.learning_weights = {
            'hba1c_weight': 0.25,
            'bmi_weight': 0.15,
            'age_weight': 0.10,
            'exercise_weight': 0.20,
            'intervention_weight': 0.30
        }
        
        # 誤差修正歷史
        self.correction_history = []
        self.target_accuracy = 0.90
    
    def calculate_ultra_precise_gl(self, food_weights: Dict[str, float], 
                                 interventions: List[NutritionIntervention],
                                 person_profile: Dict) -> Dict[str, float]:
        """超精準GL計算"""
        total_carbs = 0.0
        total_protein = 0.0
        total_fat = 0.0
        total_fiber = 0.0
        
        # 精確計算營養成分
        for food_name, weight in food_weights.items():
            portions, nutrition = self.food_db.calculate_portions(food_name, weight)
            total_carbs += nutrition['carbs']
            total_protein += nutrition['protein']
            total_fat += nutrition['fat']
            total_fiber += nutrition['fiber']
        
        # 超保守的基礎GL計算
        base_gl = total_carbs * 0.3  # 大幅降低基礎係數
        
        # 激進的營養素調整
        fiber_factor = max(0.2, 1.0 - total_fiber * 0.08)  # 增強纖維效果
        protein_factor = max(0.3, 1.0 - total_protein * 0.05)  # 增強蛋白質效果
        fat_factor = max(0.2, 1.0 - total_fat * 0.06)  # 增強脂肪效果
        
        # 應用營養素調整
        adjusted_gl = base_gl * fiber_factor * protein_factor * fat_factor
        
        # 超精準個人因子
        personal_factor = self._calculate_ultra_personal_factor(person_profile)
        
        # 超強干預效果
        intervention_reduction = self._calculate_ultra_intervention_effect(interventions, total_carbs)
        
        # 最終GL計算
        final_gl = (adjusted_gl * personal_factor * 
                   (1 - intervention_reduction) * 
                   self.ultra_calibration['base_multiplier'] * 
                   self.ultra_calibration['safety_margin'])
        
        # 確保最小值
        final_gl = max(1.0, final_gl)
        
        return {
            'base_gl': base_gl,
            'adjusted_gl': adjusted_gl,
            'final_gl': final_gl,
            'personal_factor': personal_factor,
            'intervention_reduction': intervention_reduction,
            'total_carbs': total_carbs
        }
    
    def _calculate_ultra_personal_factor(self, profile: Dict) -> float:
        """超精準個人因子計算"""
        # 基礎因子
        factor = 0.8  # 保守起始值
        
        # HbA1c影響（更精細）
        hba1c = profile.get('hba1c', 5.4)
        if hba1c > 8.0:
            hba1c_impact = 1.0 + (hba1c - 8.0) * 0.08  # 降低影響
        elif hba1c > 7.0:
            hba1c_impact = 1.0 + (hba1c - 7.0) * 0.05
        elif hba1c > 6.0:
            hba1c_impact = 1.0 + (hba1c - 6.0) * 0.03
        else:
            hba1c_impact = 0.95  # 正常值保護
        
        factor *= hba1c_impact * self.learning_weights['hba1c_weight'] + 0.75
        
        # BMI影響（更保守）
        bmi = profile.get('bmi', 23.0)
        if bmi > 30:
            bmi_impact = 1.0 + (bmi - 30) * 0.02  # 降低影響
        elif bmi > 25:
            bmi_impact = 1.0 + (bmi - 25) * 0.01
        else:
            bmi_impact = 0.95
        
        factor *= bmi_impact * self.learning_weights['bmi_weight'] + 0.85
        
        # 年齡影響（更溫和）
        age = profile.get('age', 35)
        if age > 60:
            age_impact = 1.0 + (age - 60) * 0.005
        elif age > 50:
            age_impact = 1.0 + (age - 50) * 0.003
        else:
            age_impact = 0.98
        
        factor *= age_impact * self.learning_weights['age_weight'] + 0.90
        
        # 運動保護（增強）
        exercise = profile.get('exercise_frequency', 3)
        if exercise >= 5:
            exercise_impact = 0.7  # 強保護
        elif exercise >= 3:
            exercise_impact = 0.8
        elif exercise >= 1:
            exercise_impact = 0.9
        else:
            exercise_impact = 1.1
        
        factor *= exercise_impact * self.learning_weights['exercise_weight'] + 0.80
        
        # 應用個體精度調整
        factor *= self.ultra_calibration['individual_precision']
        
        return max(0.3, min(2.0, factor))
    
    def _calculate_ultra_intervention_effect(self, interventions: List[NutritionIntervention], 
                                           base_carbs: float) -> float:
        """超強干預效果計算"""
        if not interventions:
            return 0.0
        
        total_reduction = 0.0
        
        for intervention in interventions:
            if intervention.intervention_type == InterventionType.PROTEIN:
                # 蛋白質超強效果
                protein_ratio = intervention.amount_g / max(base_carbs, 1)
                reduction = min(0.6, protein_ratio * 0.8)  # 增強效果
            
            elif intervention.intervention_type == InterventionType.FIBER:
                # 纖維超強效果
                reduction = min(0.7, intervention.amount_g * 0.06)  # 增強效果
            
            elif intervention.intervention_type == InterventionType.UNSATURATED_FAT:
                # 脂肪超強效果
                reduction = min(0.5, intervention.amount_g * 0.03)  # 增強效果
            
            else:
                # 其他干預增強
                reduction = 0.4  # 固定強效果
            
            # 時機增強
            timing_multiplier = {
                'before': 1.5,  # 增強餐前效果
                'with': 1.2,
                'after': 0.9
            }.get(intervention.timing, 1.0)
            
            total_reduction += reduction * timing_multiplier
        
        # 多重干預協同效應增強
        if len(interventions) > 1:
            synergy = 1.0 + (len(interventions) - 1) * 0.2
            total_reduction *= synergy
        
        # 應用干預放大器
        total_reduction *= self.ultra_calibration['intervention_amplifier']
        
        return min(total_reduction, 0.85)  # 最大85%降低
    
    def predict_ultra_precise_impact(self, food_weights: Dict[str, float],
                                   interventions: List[NutritionIntervention],
                                   person_profile: Dict,
                                   meal_time: datetime = None) -> Dict:
        """超精準預測"""
        if meal_time is None:
            meal_time = datetime.now()
        
        # 計算超精準GL
        gl_result = self.calculate_ultra_precise_gl(food_weights, interventions, person_profile)
        
        # 極度保守的多餐效應
        multi_meal_factor = 1.0 + len(self.meal_history) * 0.02  # 極小影響
        multi_meal_factor *= self.ultra_calibration['multi_meal_damping']
        
        # 最終預測
        final_gl = gl_result['final_gl'] * multi_meal_factor
        predicted_bg_change = final_gl * self.ultra_calibration['conversion_factor']
        
        # 應用歷史修正
        if self.correction_history:
            avg_correction = sum(self.correction_history[-10:]) / len(self.correction_history[-10:])
            predicted_bg_change *= avg_correction
        
        # 風險評估（更保守）
        if predicted_bg_change < 20:
            risk_level = "極低風險"
        elif predicted_bg_change < 40:
            risk_level = "低風險"
        elif predicted_bg_change < 60:
            risk_level = "中風險"
        elif predicted_bg_change < 80:
            risk_level = "高風險"
        else:
            risk_level = "危險"
        
        return {
            'predicted_gl': final_gl,
            'predicted_bg_change': predicted_bg_change,
            'risk_level': risk_level,
            'confidence': self._calculate_ultra_confidence(),
            'gl_details': gl_result,
            'multi_meal_factor': multi_meal_factor
        }
    
    def _calculate_ultra_confidence(self) -> float:
        """超精準置信度計算"""
        if len(self.error_history) < 3:
            return 0.95
        
        recent_errors = self.error_history[-5:]
        avg_error = sum(recent_errors) / len(recent_errors)
        
        # 基於目標準確率調整置信度
        if avg_error <= 10:
            confidence = 0.95
        elif avg_error <= 20:
            confidence = 0.85
        elif avg_error <= 30:
            confidence = 0.75
        else:
            confidence = 0.65
        
        return confidence
    
    def ultra_adaptive_learning(self, predicted_bg: float, actual_bg: float):
        """超級自適應學習"""
        error_ratio = predicted_bg / max(actual_bg, 1.0)
        error_magnitude = abs(error_ratio - 1.0)
        
        # 激進的學習率
        if error_magnitude > 0.5:
            learning_rate = 0.15
        elif error_magnitude > 0.2:
            learning_rate = 0.10
        elif error_magnitude > 0.1:
            learning_rate = 0.05
        else:
            learning_rate = 0.02
        
        # 調整核心係數
        if error_ratio > 1.05:  # 預測偏高
            self.ultra_calibration['base_multiplier'] *= (1 - learning_rate)
            self.ultra_calibration['conversion_factor'] *= (1 - learning_rate * 0.5)
        elif error_ratio < 0.95:  # 預測偏低
            self.ultra_calibration['base_multiplier'] *= (1 + learning_rate)
            self.ultra_calibration['conversion_factor'] *= (1 + learning_rate * 0.5)
        
        # 調整學習權重
        if actual_bg > 100:  # 高血糖反應
            self.learning_weights['hba1c_weight'] *= 1.05
            self.learning_weights['intervention_weight'] *= 1.1
        elif actual_bg < 50:  # 低血糖反應
            self.learning_weights['exercise_weight'] *= 1.05
            self.learning_weights['intervention_weight'] *= 0.95
        
        # 記錄修正因子
        correction_factor = actual_bg / max(predicted_bg, 1.0)
        self.correction_history.append(correction_factor)
        
        # 限制歷史長度
        if len(self.correction_history) > 20:
            self.correction_history.pop(0)
        
        # 限制係數範圍
        self.ultra_calibration['base_multiplier'] = max(0.1, min(2.0, self.ultra_calibration['base_multiplier']))
        self.ultra_calibration['conversion_factor'] = max(0.5, min(3.0, self.ultra_calibration['conversion_factor']))
        
        # 正規化學習權重
        total_weight = sum(self.learning_weights.values())
        for key in self.learning_weights:
            self.learning_weights[key] = max(0.05, min(0.5, self.learning_weights[key] / total_weight))
    
    def get_ultra_performance_stats(self) -> Dict:
        """獲取超精準性能統計"""
        stats = super().get_performance_stats()
        
        # 添加超精準特有統計
        stats.update({
            'ultra_calibration': self.ultra_calibration.copy(),
            'learning_weights': self.learning_weights.copy(),
            'correction_factors': self.correction_history[-10:] if self.correction_history else [],
            'target_achievement': stats['accuracy_rate'] >= self.target_accuracy * 100
        })
        
        return stats
