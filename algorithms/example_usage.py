"""
升糖基準指數計算器使用示例
展示如何使用算法計算不同食物和個人情況下的升糖指數
"""

from glycemic_index_calculator import (
    GlycemicIndexCalculator,
    FoodComposition,
    PersonalProfile,
    MealContext,
    CarbType,
    ProcessingLevel,
    CookingMethod,
    TimeOfDay
)

def example_1_white_rice():
    """示例1: 白米飯的升糖指數計算"""
    print("=== 示例1: 白米飯 ===")
    
    # 定義食物成分（一碗白米飯，約150g）
    white_rice = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=45.0,      # 總碳水45克
        fiber_g=0.5,             # 纖維0.5克
        protein_g=4.0,           # 蛋白質4克
        fat_g=0.3,               # 脂肪0.3克
        processing_level=ProcessingLevel.MODERATE,
        cooking_method=CookingMethod.BOILED,
        cooling_effect=False
    )
    
    # 定義個人資料（30歲健康成年人）
    person = PersonalProfile(
        age=30,
        weight_kg=70.0,
        bmi=23.0,
        body_fat_pct=15.0
    )
    
    # 定義進餐情境（午餐時間）
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=4.0
    )
    
    # 計算升糖指數
    calculator = GlycemicIndexCalculator()
    result = calculator.calculate_glycemic_index(white_rice, person, context)
    
    print(f"基礎GL: {result['base_gl']:.2f}")
    print(f"食物修正後GL: {result['food_modified_gl']:.2f}")
    print(f"最終GL: {result['final_gl']:.2f}")
    print(f"升糖速率: {result['glycemic_rate']:.2f}")
    print(f"安全閾值: {result['safety_threshold']:.2f}")
    print(f"是否安全: {'是' if result['is_safe'] else '否'}")
    print(f"風險等級: {result['risk_level']}")
    
    # 計算營養干預需求
    intervention = calculator.calculate_intervention_needs(white_rice, person, context)
    print(f"\n營養干預建議:")
    print(f"建議額外纖維: {intervention['additional_fiber_g']:.1f}克")
    print(f"建議額外蛋白質: {intervention['additional_protein_g']:.1f}克")
    print(f"建議額外健康脂肪: {intervention['additional_healthy_fat_g']:.1f}克")

def example_2_mixed_meal():
    """示例2: 混合餐食的升糖指數計算"""
    print("\n=== 示例2: 雞胸肉炒飯配蔬菜 ===")
    
    # 定義混合餐食
    mixed_meal = FoodComposition(
        carb_type=CarbType.STARCH,
        total_carbs_g=55.0,      # 總碳水55克
        fiber_g=8.0,             # 纖維8克（含蔬菜）
        protein_g=25.0,          # 蛋白質25克（雞胸肉）
        fat_g=12.0,              # 脂肪12克（烹調油）
        processing_level=ProcessingLevel.MINIMAL,
        cooking_method=CookingMethod.FRIED,
        cooling_effect=False
    )
    
    # 定義個人資料（45歲，輕度超重）
    person = PersonalProfile(
        age=45,
        weight_kg=80.0,
        bmi=26.5,
        body_fat_pct=22.0
    )
    
    # 定義進餐情境（晚餐時間）
    context = MealContext(
        time_of_day=TimeOfDay.EVENING,
        fasting_hours=6.0
    )
    
    calculator = GlycemicIndexCalculator()
    result = calculator.calculate_glycemic_index(mixed_meal, person, context)
    
    print(f"基礎GL: {result['base_gl']:.2f}")
    print(f"食物修正後GL: {result['food_modified_gl']:.2f}")
    print(f"最終GL: {result['final_gl']:.2f}")
    print(f"升糖速率: {result['glycemic_rate']:.2f}")
    print(f"安全閾值: {result['safety_threshold']:.2f}")
    print(f"是否安全: {'是' if result['is_safe'] else '否'}")
    print(f"風險等級: {result['risk_level']}")

def example_3_high_risk_scenario():
    """示例3: 高風險情境（糖尿病前期患者）"""
    print("\n=== 示例3: 高風險情境 - 白麵包早餐 ===")
    
    # 定義高升糖食物（白麵包配果醬）
    white_bread_jam = FoodComposition(
        carb_type=CarbType.SIMPLE_SUGAR,  # 含大量添加糖
        total_carbs_g=60.0,
        fiber_g=2.0,
        protein_g=8.0,
        fat_g=3.0,
        processing_level=ProcessingLevel.HIGHLY,
        cooking_method=CookingMethod.BAKED,
        cooling_effect=False
    )
    
    # 定義高風險個人資料（55歲，肥胖）
    high_risk_person = PersonalProfile(
        age=55,
        weight_kg=90.0,
        bmi=30.0,
        body_fat_pct=28.0
    )
    
    # 定義進餐情境（早餐，長時間禁食後）
    context = MealContext(
        time_of_day=TimeOfDay.MORNING,
        fasting_hours=12.0
    )
    
    calculator = GlycemicIndexCalculator()
    result = calculator.calculate_glycemic_index(white_bread_jam, high_risk_person, context)
    
    print(f"基礎GL: {result['base_gl']:.2f}")
    print(f"食物修正後GL: {result['food_modified_gl']:.2f}")
    print(f"最終GL: {result['final_gl']:.2f}")
    print(f"升糖速率: {result['glycemic_rate']:.2f}")
    print(f"安全閾值: {result['safety_threshold']:.2f}")
    print(f"是否安全: {'是' if result['is_safe'] else '否'}")
    print(f"風險等級: {result['risk_level']}")
    
    # 計算強化營養干預需求
    intervention = calculator.calculate_intervention_needs(
        white_bread_jam, high_risk_person, context, target_reduction=0.5
    )
    print(f"\n強化營養干預建議（目標降低50%）:")
    print(f"建議額外纖維: {intervention['additional_fiber_g']:.1f}克")
    print(f"建議額外蛋白質: {intervention['additional_protein_g']:.1f}克")
    print(f"建議額外健康脂肪: {intervention['additional_healthy_fat_g']:.1f}克")

def example_4_optimized_meal():
    """示例4: 優化後的低升糖餐食"""
    print("\n=== 示例4: 優化低升糖餐食 - 藜麥沙拉配堅果 ===")
    
    # 定義優化餐食
    quinoa_salad = FoodComposition(
        carb_type=CarbType.COMPLEX_CARB,
        total_carbs_g=35.0,
        fiber_g=12.0,            # 高纖維
        protein_g=18.0,          # 高蛋白
        fat_g=15.0,              # 健康脂肪（堅果）
        processing_level=ProcessingLevel.MINIMAL,
        cooking_method=CookingMethod.STEAMED,
        cooling_effect=True      # 冷藏過，增加抗性澱粉
    )
    
    # 同樣的高風險個人
    high_risk_person = PersonalProfile(
        age=55,
        weight_kg=90.0,
        bmi=30.0,
        body_fat_pct=28.0
    )
    
    context = MealContext(
        time_of_day=TimeOfDay.MIDDAY,
        fasting_hours=5.0
    )
    
    calculator = GlycemicIndexCalculator()
    result = calculator.calculate_glycemic_index(quinoa_salad, high_risk_person, context)
    
    print(f"基礎GL: {result['base_gl']:.2f}")
    print(f"食物修正後GL: {result['food_modified_gl']:.2f}")
    print(f"最終GL: {result['final_gl']:.2f}")
    print(f"升糖速率: {result['glycemic_rate']:.2f}")
    print(f"安全閾值: {result['safety_threshold']:.2f}")
    print(f"是否安全: {'是' if result['is_safe'] else '否'}")
    print(f"風險等級: {result['risk_level']}")

def comparison_analysis():
    """比較分析：不同食物的升糖效果"""
    print("\n=== 比較分析：不同食物的升糖效果 ===")
    
    # 標準個人資料
    standard_person = PersonalProfile(age=35, weight_kg=70, bmi=23, body_fat_pct=18)
    standard_context = MealContext(time_of_day=TimeOfDay.MIDDAY, fasting_hours=5)
    
    calculator = GlycemicIndexCalculator()
    
    # 測試不同食物
    foods = [
        ("白米飯", FoodComposition(CarbType.STARCH, 45, 0.5, 4, 0.3, 
                                ProcessingLevel.MODERATE, CookingMethod.BOILED)),
        ("糙米飯", FoodComposition(CarbType.STARCH, 45, 4.0, 5, 2.0, 
                                ProcessingLevel.MINIMAL, CookingMethod.BOILED)),
        ("白麵包", FoodComposition(CarbType.STARCH, 50, 2.0, 8, 3, 
                                ProcessingLevel.HIGHLY, CookingMethod.BAKED)),
        ("全麥麵包", FoodComposition(CarbType.COMPLEX_CARB, 45, 8.0, 12, 4, 
                                  ProcessingLevel.MINIMAL, CookingMethod.BAKED)),
        ("蘋果", FoodComposition(CarbType.SIMPLE_SUGAR, 25, 4.0, 0.5, 0.2, 
                               ProcessingLevel.RAW, CookingMethod.RAW))
    ]
    
    print(f"{'食物':<10} {'升糖速率':<10} {'風險等級':<10}")
    print("-" * 35)
    
    for name, food in foods:
        result = calculator.calculate_glycemic_index(food, standard_person, standard_context)
        print(f"{name:<10} {result['glycemic_rate']:<10.2f} {result['risk_level']:<10}")

if __name__ == "__main__":
    # 運行所有示例
    example_1_white_rice()
    example_2_mixed_meal()
    example_3_high_risk_scenario()
    example_4_optimized_meal()
    comparison_analysis()
    
    print("\n=== 總結 ===")
    print("1. 升糖基準指數綜合考慮了食物成分、個人生理參數和進餐情境")
    print("2. 高纖維、高蛋白、健康脂肪可有效降低升糖速率")
    print("3. 個人化因子（年齡、BMI、體脂率）顯著影響升糖反應")
    print("4. 進餐時間和禁食時間也是重要影響因素")
    print("5. 通過營養干預可以有效控制血糖升糖速度")
