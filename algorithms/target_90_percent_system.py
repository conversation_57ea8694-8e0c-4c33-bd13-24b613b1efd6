"""
90%目標達成系統
深度分析：為什麼60%→90%如此困難？
策略：分層處理 + 特殊人群識別 + 精準校準
"""

from final_breakthrough_formula import FinalBreakthroughFormula
from formula_validation_test import ClinicalDataValidator
from test_practical_formulas import create_practical_test_person
from advanced_glycemic_calculator import FoodComposition, CarbType, ProcessingLevel, CookingMethod
import math

def np_mean(data):
    return sum(data) / len(data) if data else 0

class Target90PercentSystem:
    """90%目標達成系統"""
    
    def __init__(self):
        self.base_formula = FinalBreakthroughFormula()
        self.validator = ClinicalDataValidator()
        
        # 分析60%成功公式的問題
        self.problem_analysis = {
            'high_error_patients': [],      # 高誤差患者
            'patient_clusters': {},         # 患者聚類
            'error_patterns': {},           # 誤差模式
            'special_cases': []             # 特殊案例
        }
        
        # 分層處理策略
        self.layered_strategy = {
            'normal': {                     # 正常患者（預期60%）
                'base_multiplier': 2.022,
                'conversion_factor': 2.621,
                'method': 'standard'
            },
            'high_hba1c': {                 # 高HbA1c患者
                'base_multiplier': 1.8,
                'conversion_factor': 2.2,
                'method': 'conservative'
            },
            'low_hba1c': {                  # 低HbA1c患者
                'base_multiplier': 2.4,
                'conversion_factor': 3.0,
                'method': 'enhanced'
            },
            'high_bmi': {                   # 高BMI患者
                'base_multiplier': 1.9,
                'conversion_factor': 2.3,
                'method': 'adjusted'
            },
            'elderly': {                    # 老年患者
                'base_multiplier': 1.7,
                'conversion_factor': 2.1,
                'method': 'gentle'
            },
            'outlier': {                    # 異常患者
                'base_multiplier': 1.5,
                'conversion_factor': 1.8,
                'method': 'extreme_conservative'
            }
        }
    
    def analyze_60_percent_failures(self):
        """分析60%成功公式的失敗案例"""
        print("🔍 深度分析60%成功公式的失敗案例...")
        
        errors = []
        patient_errors = []
        
        # 使用原始成功公式測試所有患者
        for i, patient_data in enumerate(self.validator.getgoal_patients):
            person = create_practical_test_person(patient_data)
            
            # 標準測試餐
            standard_meal = FoodComposition(
                carb_type=CarbType.STARCH,
                total_carbs_g=78.0,
                fiber_g=3.0,
                protein_g=15.0,
                fat_g=8.0,
                processing_level=ProcessingLevel.MODERATE,
                cooking_method=CookingMethod.BOILED,
                sugar_g=5.0,
                starch_g=73.0
            )
            
            # 使用成功公式預測
            context = self._create_context()
            meal_history = self._create_meal_history()
            
            result = self.base_formula.calculate_comprehensive_glycemic_index(
                standard_meal, person, context, meal_history
            )
            
            predicted_bg = result['final_gl'] * 2.621  # 使用成功的轉換係數
            actual_bg = abs(patient_data['actual_ppg_change'])
            
            error = abs(predicted_bg - actual_bg) / actual_bg * 100
            errors.append(error)
            
            patient_info = {
                'index': i,
                'error': error,
                'predicted': predicted_bg,
                'actual': actual_bg,
                'hba1c': patient_data['hba1c'],
                'bmi': patient_data['bmi'],
                'age': patient_data['age'],
                'accurate': error <= 10.0
            }
            patient_errors.append(patient_info)
        
        # 分析失敗案例
        high_error_patients = [p for p in patient_errors if p['error'] > 10.0]
        accurate_patients = [p for p in patient_errors if p['error'] <= 10.0]
        
        print(f"✅ 準確患者: {len(accurate_patients)} ({len(accurate_patients)/len(patient_errors)*100:.1f}%)")
        print(f"❌ 高誤差患者: {len(high_error_patients)} ({len(high_error_patients)/len(patient_errors)*100:.1f}%)")
        
        # 分析高誤差患者特徵
        self._analyze_high_error_patterns(high_error_patients)
        
        return patient_errors, high_error_patients, accurate_patients
    
    def _analyze_high_error_patterns(self, high_error_patients):
        """分析高誤差患者模式"""
        print(f"\n📊 高誤差患者特徵分析:")
        
        if not high_error_patients:
            return
        
        # HbA1c分析
        hba1c_groups = {'<6.0': [], '6.0-7.0': [], '7.0-8.0': [], '>8.0': []}
        for p in high_error_patients:
            if p['hba1c'] < 6.0:
                hba1c_groups['<6.0'].append(p)
            elif p['hba1c'] < 7.0:
                hba1c_groups['6.0-7.0'].append(p)
            elif p['hba1c'] < 8.0:
                hba1c_groups['7.0-8.0'].append(p)
            else:
                hba1c_groups['>8.0'].append(p)
        
        print("HbA1c分布:")
        for group, patients in hba1c_groups.items():
            if patients:
                avg_error = np_mean([p['error'] for p in patients])
                print(f"  {group}: {len(patients)}人, 平均誤差{avg_error:.1f}%")
        
        # BMI分析
        bmi_groups = {'<25': [], '25-30': [], '>30': []}
        for p in high_error_patients:
            if p['bmi'] < 25:
                bmi_groups['<25'].append(p)
            elif p['bmi'] < 30:
                bmi_groups['25-30'].append(p)
            else:
                bmi_groups['>30'].append(p)
        
        print("BMI分布:")
        for group, patients in bmi_groups.items():
            if patients:
                avg_error = np_mean([p['error'] for p in patients])
                print(f"  {group}: {len(patients)}人, 平均誤差{avg_error:.1f}%")
        
        # 年齡分析
        age_groups = {'<50': [], '50-65': [], '>65': []}
        for p in high_error_patients:
            if p['age'] < 50:
                age_groups['<50'].append(p)
            elif p['age'] < 65:
                age_groups['50-65'].append(p)
            else:
                age_groups['>65'].append(p)
        
        print("年齡分布:")
        for group, patients in age_groups.items():
            if patients:
                avg_error = np_mean([p['error'] for p in patients])
                print(f"  {group}: {len(patients)}人, 平均誤差{avg_error:.1f}%")
        
        # 找出最問題的患者
        worst_patients = sorted(high_error_patients, key=lambda x: x['error'], reverse=True)[:3]
        print(f"\n⚠️ 最高誤差患者:")
        for p in worst_patients:
            print(f"  患者{p['index']}: {p['error']:.1f}%誤差, HbA1c{p['hba1c']:.1f}, BMI{p['bmi']:.1f}, 年齡{p['age']}")
    
    def classify_patients(self, patient_errors):
        """患者分類"""
        print(f"\n🎯 患者分類策略...")
        
        classifications = {
            'normal': [],
            'high_hba1c': [],
            'low_hba1c': [],
            'high_bmi': [],
            'elderly': [],
            'outlier': []
        }
        
        for p in patient_errors:
            # 異常值檢測（誤差>50%）
            if p['error'] > 50.0:
                classifications['outlier'].append(p)
            # 高HbA1c
            elif p['hba1c'] > 8.0:
                classifications['high_hba1c'].append(p)
            # 低HbA1c但高誤差
            elif p['hba1c'] < 6.0 and p['error'] > 15.0:
                classifications['low_hba1c'].append(p)
            # 高BMI
            elif p['bmi'] > 30.0:
                classifications['high_bmi'].append(p)
            # 老年人
            elif p['age'] > 70:
                classifications['elderly'].append(p)
            # 正常患者
            else:
                classifications['normal'].append(p)
        
        print("患者分類結果:")
        for category, patients in classifications.items():
            if patients:
                avg_error = np_mean([p['error'] for p in patients])
                accuracy = sum(1 for p in patients if p['accurate']) / len(patients) * 100
                print(f"  {category}: {len(patients)}人, 平均誤差{avg_error:.1f}%, 準確率{accuracy:.1f}%")
        
        return classifications
    
    def predict_with_layered_strategy(self, patient_index: int, patient_category: str) -> dict:
        """使用分層策略預測"""
        patient_data = self.validator.getgoal_patients[patient_index]
        person = create_practical_test_person(patient_data)
        
        # 獲取該類別的參數
        strategy = self.layered_strategy[patient_category]
        
        # 標準測試餐
        standard_meal = FoodComposition(
            carb_type=CarbType.STARCH,
            total_carbs_g=78.0,
            fiber_g=3.0,
            protein_g=15.0,
            fat_g=8.0,
            processing_level=ProcessingLevel.MODERATE,
            cooking_method=CookingMethod.BOILED,
            sugar_g=5.0,
            starch_g=73.0
        )
        
        # 使用基礎公式計算
        context = self._create_context()
        meal_history = self._create_meal_history()
        
        result = self.base_formula.calculate_comprehensive_glycemic_index(
            standard_meal, person, context, meal_history
        )
        
        # 應用分層策略調整
        adjusted_gl = result['final_gl'] * strategy['base_multiplier'] / 2.022  # 相對於原始係數調整
        predicted_bg = adjusted_gl * strategy['conversion_factor']
        
        actual_bg = abs(patient_data['actual_ppg_change'])
        error = abs(predicted_bg - actual_bg) / actual_bg * 100
        
        return {
            'patient_index': patient_index,
            'category': patient_category,
            'method': strategy['method'],
            'predicted_bg': predicted_bg,
            'actual_bg': actual_bg,
            'error': error,
            'accurate': error <= 10.0,
            'base_gl': result['final_gl'],
            'adjusted_gl': adjusted_gl
        }
    
    def test_layered_strategy(self, classifications):
        """測試分層策略"""
        print(f"\n🧪 測試分層策略...")
        
        all_results = []
        category_results = {}
        
        for category, patients in classifications.items():
            if not patients:
                continue
                
            category_results[category] = []
            
            for patient in patients:
                result = self.predict_with_layered_strategy(patient['index'], category)
                all_results.append(result)
                category_results[category].append(result)
        
        # 計算總體結果
        total_accurate = sum(1 for r in all_results if r['accurate'])
        total_accuracy = total_accurate / len(all_results) * 100
        avg_error = np_mean([r['error'] for r in all_results])
        
        print(f"\n📊 分層策略總體結果:")
        print(f"總體準確率: {total_accuracy:.1f}%")
        print(f"平均誤差: {avg_error:.1f}%")
        print(f"準確患者: {total_accurate}/{len(all_results)}")
        
        # 各類別結果
        print(f"\n📋 各類別結果:")
        for category, results in category_results.items():
            if results:
                cat_accuracy = sum(1 for r in results if r['accurate']) / len(results) * 100
                cat_avg_error = np_mean([r['error'] for r in results])
                print(f"  {category}: {cat_accuracy:.1f}%準確率, {cat_avg_error:.1f}%平均誤差")
        
        return all_results, total_accuracy
    
    def optimize_for_90_percent(self, classifications, max_iterations=20):
        """優化達到90%目標"""
        print(f"\n🎯 優化達到90%目標...")
        
        best_accuracy = 0.0
        best_strategy = None
        
        for iteration in range(1, max_iterations + 1):
            print(f"\n--- 第{iteration}次優化 ---")
            
            # 測試當前策略
            results, accuracy = self.test_layered_strategy(classifications)
            
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_strategy = {k: v.copy() for k, v in self.layered_strategy.items()}
                print(f"🎯 新的最佳準確率: {accuracy:.1f}%")
            
            # 檢查是否達到目標
            if accuracy >= 90.0:
                print(f"\n🎉 成功達到90%目標！")
                print(f"第{iteration}次優化達到{accuracy:.1f}%準確率")
                break
            
            # 基於結果調整策略
            self._adjust_strategy_based_on_results(results)
            
            progress = accuracy / 90.0 * 100
            print(f"進度: {progress:.1f}% (還差{90.0 - accuracy:.1f}%)")
        
        return best_accuracy, best_strategy
    
    def _adjust_strategy_based_on_results(self, results):
        """基於結果調整策略"""
        # 按類別分組結果
        category_results = {}
        for result in results:
            category = result['category']
            if category not in category_results:
                category_results[category] = []
            category_results[category].append(result)
        
        # 調整每個類別的參數
        for category, cat_results in category_results.items():
            if not cat_results:
                continue
                
            # 計算該類別的準確率
            cat_accuracy = sum(1 for r in cat_results if r['accurate']) / len(cat_results)
            
            # 如果準確率低於80%，調整參數
            if cat_accuracy < 0.8:
                # 分析誤差方向
                over_predictions = [r for r in cat_results if r['predicted_bg'] > r['actual_bg']]
                under_predictions = [r for r in cat_results if r['predicted_bg'] < r['actual_bg']]
                
                if len(over_predictions) > len(under_predictions):
                    # 預測偏高，降低係數
                    self.layered_strategy[category]['base_multiplier'] *= 0.95
                    self.layered_strategy[category]['conversion_factor'] *= 0.97
                else:
                    # 預測偏低，提高係數
                    self.layered_strategy[category]['base_multiplier'] *= 1.05
                    self.layered_strategy[category]['conversion_factor'] *= 1.03
                
                # 限制係數範圍
                self.layered_strategy[category]['base_multiplier'] = max(0.5, min(4.0, self.layered_strategy[category]['base_multiplier']))
                self.layered_strategy[category]['conversion_factor'] = max(1.0, min(5.0, self.layered_strategy[category]['conversion_factor']))
    
    def _create_context(self):
        """創建餐食情境"""
        from advanced_glycemic_calculator import MealContext, TimeOfDay
        return MealContext(
            time_of_day=TimeOfDay.MIDDAY,
            fasting_hours=4.0,
            stress_level=3
        )
    
    def _create_meal_history(self):
        """創建餐食歷史"""
        from advanced_glycemic_calculator import MealHistory
        return MealHistory()
    
    def run_90_percent_challenge(self):
        """運行90%挑戰"""
        print("🚀 開始90%準確率挑戰")
        print("策略：深度分析 + 患者分類 + 分層處理")
        print("=" * 60)
        
        # 1. 分析60%成功公式的失敗案例
        patient_errors, high_error_patients, accurate_patients = self.analyze_60_percent_failures()
        
        # 2. 患者分類
        classifications = self.classify_patients(patient_errors)
        
        # 3. 優化達到90%
        best_accuracy, best_strategy = self.optimize_for_90_percent(classifications)
        
        # 4. 最終驗證
        final_results, final_accuracy = self.test_layered_strategy(classifications)
        
        print(f"\n" + "=" * 60)
        print(f"🏁 90%挑戰完成")
        print(f"=" * 60)
        print(f"最佳準確率: {best_accuracy:.1f}%")
        print(f"最終準確率: {final_accuracy:.1f}%")
        print(f"目標達成: {'✅ 是' if final_accuracy >= 90.0 else '❌ 否'}")
        
        return final_accuracy >= 90.0, final_accuracy, best_strategy

def main():
    """主函數"""
    system = Target90PercentSystem()
    success, accuracy, strategy = system.run_90_percent_challenge()
    
    if success:
        print(f"\n🎉 成功達到90%目標！最終準確率: {accuracy:.1f}%")
    else:
        print(f"\n📈 未達90%目標，最終準確率: {accuracy:.1f}%")
    
    return success, accuracy

if __name__ == "__main__":
    main()
