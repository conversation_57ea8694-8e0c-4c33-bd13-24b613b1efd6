"""
A11終極優化公式 - 基於A1-A10測試結果的最終優化版本
目標：將預測誤差降低到9%以下

基於測試結果的優化策略：
1. A6 (<PERSON><PERSON>濾波) 穩定性最佳 (0.083)
2. A2 (微生物強化) 和 A7 (代謝狀態) 表現優秀
3. A3 (ARIMA) 在某些情況下波動過大，需要限制
4. 需要更精確的權重分配和動態調整機制
"""

import math
from typing import Dict, List
from formula_series_a1_a10 import *

class FormulaA11Ultimate(AdvancedGlycemicCalculator):
    """A11終極優化公式 - 基於測試結果的最佳組合"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A11 - 終極優化版"
        self.version = "11.0"
        
        # 基於測試結果的最佳子模塊組合
        self.kalman_module = FormulaA6()  # 最穩定
        self.microbiome_module = FormulaA2()  # 高精度
        self.metabolic_module = FormulaA7()  # 高精度
        self.vascular_module = FormulaA8()  # 中等精度
        
        # 動態權重系統
        self.base_weights = {
            'kalman': 0.35,      # 最高權重給最穩定的
            'microbiome': 0.25,  # 微生物因子很重要
            'metabolic': 0.25,   # 代謝狀態很重要
            'vascular': 0.15     # 血管因子輔助
        }
        
        # 學習記錄
        self.prediction_history = []
        self.error_history = []
    
    def calculate_dynamic_weights(self, person: ExtendedPersonalProfile, 
                                context: MealContext) -> Dict[str, float]:
        """基於個人特徵動態調整權重"""
        weights = self.base_weights.copy()
        
        # 根據HbA1c調整權重
        if person.hba1c > 8.0:
            # 高HbA1c患者，增加代謝和微生物權重
            weights['metabolic'] += 0.1
            weights['microbiome'] += 0.1
            weights['kalman'] -= 0.1
            weights['vascular'] -= 0.1
        elif person.hba1c < 6.0:
            # 低HbA1c患者，增加Kalman權重
            weights['kalman'] += 0.1
            weights['metabolic'] -= 0.05
            weights['microbiome'] -= 0.05
        
        # 根據BMI調整權重
        if person.bmi > 30:
            # 肥胖患者，增加代謝權重
            weights['metabolic'] += 0.1
            weights['vascular'] += 0.05
            weights['kalman'] -= 0.1
            weights['microbiome'] -= 0.05
        
        # 根據年齡調整權重
        if person.age > 65:
            # 老年患者，增加血管權重
            weights['vascular'] += 0.1
            weights['kalman'] += 0.05
            weights['metabolic'] -= 0.1
            weights['microbiome'] -= 0.05
        
        # 確保權重總和為1
        total_weight = sum(weights.values())
        weights = {k: v/total_weight for k, v in weights.items()}
        
        return weights
    
    def calculate_confidence_adjustment(self, predictions: List[float]) -> float:
        """基於預測一致性計算置信度調整因子"""
        if len(predictions) < 2:
            return 1.0
        
        # 計算預測的變異係數
        mean_pred = sum(predictions) / len(predictions)
        variance = sum((x - mean_pred) ** 2 for x in predictions) / len(predictions)
        std_pred = variance ** 0.5
        
        if mean_pred == 0:
            return 1.0
        
        cv = std_pred / mean_pred  # 變異係數
        
        # 變異係數越小，置信度越高，調整因子越接近1
        confidence_factor = 1.0 / (1.0 + cv * 2.0)
        
        return confidence_factor
    
    def apply_outlier_detection(self, predictions: List[float]) -> List[float]:
        """異常值檢測和修正"""
        if len(predictions) < 3:
            return predictions
        
        # 計算四分位數
        sorted_preds = sorted(predictions)
        n = len(sorted_preds)
        q1 = sorted_preds[n//4]
        q3 = sorted_preds[3*n//4]
        iqr = q3 - q1
        
        # 異常值閾值
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # 修正異常值
        corrected_predictions = []
        for pred in predictions:
            if pred < lower_bound:
                corrected_predictions.append(q1)
            elif pred > upper_bound:
                corrected_predictions.append(q3)
            else:
                corrected_predictions.append(pred)
        
        return corrected_predictions
    
    def calculate_meal_context_adjustment(self, context: MealContext, 
                                        meal_history: MealHistory) -> float:
        """餐食情境動態調整"""
        adjustment = 1.0
        
        # 禁食時間影響
        if context.fasting_hours > 12:
            adjustment *= 1.1  # 長時間禁食增加升糖反應
        elif context.fasting_hours < 3:
            adjustment *= 0.9  # 短時間禁食降低升糖反應
        
        # 一日內餐次影響
        recent_meals = len([m for m in meal_history.meals if m[0] > -6])  # 6小時內的餐次
        if recent_meals > 2:
            adjustment *= 0.85  # 頻繁進食降低升糖反應
        
        # 時間段影響
        if context.time_of_day == TimeOfDay.MORNING:
            adjustment *= 1.05  # 晨間胰島素敏感性較低
        elif context.time_of_day == TimeOfDay.EVENING:
            adjustment *= 1.02  # 晚間略微增加
        
        # 壓力水平影響
        stress_factor = 1.0 + (context.stress_level - 3) * 0.05
        adjustment *= stress_factor
        
        return adjustment
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取各子模塊的預測
        predictions = {}
        
        # Kalman濾波預測（最穩定）
        kalman_result = self.kalman_module.calculate_comprehensive_glycemic_index(
            food, person, context, meal_history
        )
        predictions['kalman'] = kalman_result['final_gl']
        
        # 微生物因子預測
        microbiome_factor = self.microbiome_module.calculate_microbiome_factor(person)
        base_result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        predictions['microbiome'] = base_result['final_gl'] * microbiome_factor
        
        # 代謝狀態預測
        metabolic_factor = self.metabolic_module.calculate_metabolic_state_factor(person, context)
        predictions['metabolic'] = base_result['final_gl'] * metabolic_factor
        
        # 血管功能預測
        vascular_factor = self.vascular_module.calculate_vascular_factor(person)
        predictions['vascular'] = base_result['final_gl'] * vascular_factor
        
        # 異常值檢測和修正
        pred_values = list(predictions.values())
        corrected_values = self.apply_outlier_detection(pred_values)
        
        # 更新預測值
        pred_keys = list(predictions.keys())
        for i, key in enumerate(pred_keys):
            predictions[key] = corrected_values[i]
        
        # 動態權重計算
        weights = self.calculate_dynamic_weights(person, context)
        
        # 加權平均預測
        weighted_prediction = sum(predictions[key] * weights[key] 
                                for key in predictions.keys())
        
        # 置信度調整
        confidence_factor = self.calculate_confidence_adjustment(corrected_values)
        
        # 餐食情境調整
        context_adjustment = self.calculate_meal_context_adjustment(context, meal_history)
        
        # 最終預測
        final_gl = weighted_prediction * confidence_factor * context_adjustment
        
        # 構建結果
        result = base_result.copy()
        result['final_gl'] = final_gl
        result['glycemic_rate'] = final_gl / result['peak_time_hours']
        
        # 重新評估安全性
        safety_threshold = self._calculate_dynamic_safety_threshold(person, context)
        result['is_safe'] = result['glycemic_rate'] <= safety_threshold
        result['risk_level'] = self._assess_comprehensive_risk(result['glycemic_rate'], safety_threshold, person)
        
        # 添加詳細信息
        result['individual_predictions'] = predictions
        result['weights_used'] = weights
        result['confidence_factor'] = confidence_factor
        result['context_adjustment'] = context_adjustment
        result['ensemble_confidence'] = confidence_factor
        
        # 記錄預測歷史
        self.prediction_history.append(final_gl)
        if len(self.prediction_history) > 100:  # 保持最近100次預測
            self.prediction_history.pop(0)
        
        return result
    
    def update_with_actual_result(self, predicted_gl: float, actual_gl: float):
        """使用實際結果更新模型（在線學習）"""
        error = abs(predicted_gl - actual_gl) / max(actual_gl, 1.0)
        self.error_history.append(error)
        
        if len(self.error_history) > 50:  # 保持最近50次誤差
            self.error_history.pop(0)
        
        # 基於誤差調整基礎權重（簡單的在線學習）
        if len(self.error_history) >= 10:
            recent_avg_error = sum(self.error_history[-10:]) / 10
            if recent_avg_error > 0.15:  # 如果誤差過大，增加Kalman權重
                self.base_weights['kalman'] = min(0.5, self.base_weights['kalman'] + 0.02)
                self.base_weights['microbiome'] *= 0.98
                self.base_weights['metabolic'] *= 0.98
                self.base_weights['vascular'] *= 0.98
        
        # 重新歸一化權重
        total_weight = sum(self.base_weights.values())
        self.base_weights = {k: v/total_weight for k, v in self.base_weights.items()}
    
    def get_model_performance(self) -> Dict[str, float]:
        """獲取模型性能統計"""
        if not self.error_history:
            return {'avg_error': 0, 'std_error': 0, 'accuracy_rate': 0}
        
        avg_error = sum(self.error_history) / len(self.error_history)
        variance = sum((e - avg_error) ** 2 for e in self.error_history) / len(self.error_history)
        std_error = variance ** 0.5
        accuracy_rate = sum(1 for e in self.error_history if e <= 0.09) / len(self.error_history)
        
        return {
            'avg_error': avg_error,
            'std_error': std_error,
            'accuracy_rate': accuracy_rate,
            'total_predictions': len(self.error_history)
        }
