"""
升糖基準指數公式系列 A1-A10
基於三大研究共通性分析，設計十個不同的預測公式
目標：將預測誤差降低到9%以下

研究共通性分析：
1. <PERSON> et al. (2021) - 韓國GL預測：強調營養素交互作用
2. <PERSON><PERSON><PERSON> et al. (2015) - 以色列個人化：強調微生物和遺傳因素  
3. <PERSON> et al. (2020) - 英國PREDICT：強調時間序列和代謝變異

遺漏參數識別：
- 腸道轉運時間 (Gut Transit Time)
- 胰島素分泌速率 (Insulin Secretion Rate)
- 肝糖原狀態 (Hepatic Glycogen Status)
- 肌肉糖原利用率 (Muscle Glycogen Utilization)
- 腸道微生物多樣性指數 (Microbiome Diversity Index)
- 血管內皮功能 (Endothelial Function)
- 自主神經活性 (Autonomic Nervous Activity)
- 炎症標記物水平 (Inflammatory Markers)
"""

import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum
from advanced_glycemic_calculator import (
    AdvancedGlycemicCalculator, FoodComposition, PersonalProfile, 
    MealContext, MealHistory, CarbType, ProcessingLevel, CookingMethod, TimeOfDay
)

@dataclass
class ExtendedPersonalProfile(PersonalProfile):
    """擴展個人資料 - 新增遺漏參數"""
    # 消化系統參數
    gut_transit_time_hours: float = 24.0        # 腸道轉運時間
    gastric_ph: float = 2.0                     # 胃酸pH值
    bile_acid_level: float = 1.0                # 膽汁酸水平
    
    # 代謝參數
    insulin_secretion_rate: float = 1.0         # 胰島素分泌速率
    hepatic_glycogen_pct: float = 70.0          # 肝糖原儲存百分比
    muscle_glycogen_pct: float = 80.0           # 肌糖原儲存百分比
    glucagon_sensitivity: float = 1.0           # 胰高血糖素敏感性
    
    # 微生物參數
    microbiome_diversity: float = 0.8           # 腸道微生物多樣性 (0-1)
    beneficial_bacteria_ratio: float = 0.7      # 有益菌比例
    scfa_production: float = 1.0                # 短鏈脂肪酸產生能力
    
    # 血管和神經參數
    endothelial_function: float = 1.0           # 血管內皮功能
    autonomic_balance: float = 1.0              # 自主神經平衡
    sympathetic_activity: float = 1.0           # 交感神經活性
    
    # 炎症和免疫參數
    crp_level: float = 1.0                      # C反應蛋白水平
    il6_level: float = 1.0                      # 白介素-6水平
    tnf_alpha: float = 1.0                      # 腫瘤壞死因子-α
    
    # 遺傳和表觀遺傳參數
    genetic_risk_score: float = 1.0             # 遺傳風險評分
    epigenetic_age: float = 0.0                 # 表觀遺傳年齡
    
    # 生活方式參數
    meal_frequency: int = 3                     # 每日用餐頻率
    eating_speed: float = 1.0                   # 進食速度 (0.5-2.0)
    chewing_efficiency: float = 1.0             # 咀嚼效率
    hydration_level: float = 1.0                # 水合狀態

class FormulaA1(AdvancedGlycemicCalculator):
    """A1公式 - 原始進階公式（基準版本）"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A1 - 進階基準公式"
        self.version = "1.0"

class FormulaA2(AdvancedGlycemicCalculator):
    """A2公式 - 腸道微生物強化版"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A2 - 腸道微生物強化版"
        self.version = "2.0"
    
    def calculate_microbiome_factor(self, person: ExtendedPersonalProfile) -> float:
        """計算腸道微生物影響因子"""
        # 微生物多樣性影響
        diversity_factor = 0.5 + (person.microbiome_diversity * 0.5)
        
        # 有益菌比例影響
        beneficial_factor = 0.7 + (person.beneficial_bacteria_ratio * 0.3)
        
        # 短鏈脂肪酸產生能力
        scfa_factor = 0.8 + (person.scfa_production * 0.2)
        
        # 腸道轉運時間影響
        transit_factor = 1.0 + (person.gut_transit_time_hours - 24) * 0.01
        
        return diversity_factor * beneficial_factor * scfa_factor * transit_factor
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # 應用微生物修正
        microbiome_factor = self.calculate_microbiome_factor(person)
        result['final_gl'] *= microbiome_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        
        # 重新評估風險
        safety_threshold = self._calculate_dynamic_safety_threshold(person, context)
        result['is_safe'] = result['glycemic_rate'] <= safety_threshold
        result['risk_level'] = self._assess_comprehensive_risk(result['glycemic_rate'], safety_threshold, person)
        
        return result

class FormulaA3(AdvancedGlycemicCalculator):
    """A3公式 - 時間序列ARIMA預測版"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A3 - ARIMA時間序列預測版"
        self.version = "3.0"
        self.historical_data = []  # 存儲歷史血糖數據
    
    def arima_prediction(self, historical_gl: List[float], steps: int = 1) -> float:
        """簡化的ARIMA預測算法"""
        if len(historical_gl) < 3:
            return 0.0
        
        # AR(1)模型：y_t = φ*y_{t-1} + ε_t
        phi = 0.7  # 自回歸係數
        
        # MA(1)模型：考慮前一期誤差
        theta = 0.3  # 移動平均係數
        
        # 差分處理（I部分）
        diff = [historical_gl[i] - historical_gl[i-1] for i in range(1, len(historical_gl))]
        
        if len(diff) == 0:
            return historical_gl[-1]
        
        # 預測下一期差分值
        predicted_diff = phi * diff[-1] if len(diff) > 0 else 0
        
        # 還原預測值
        predicted_gl = historical_gl[-1] + predicted_diff
        
        return max(0, predicted_gl)
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # 提取歷史GL數據
        historical_gl = [meal[1] for meal in meal_history.meals[-10:]]  # 最近10餐
        
        if len(historical_gl) >= 3:
            # ARIMA預測調整
            arima_adjustment = self.arima_prediction(historical_gl)
            trend_factor = 1.0 + (arima_adjustment * 0.1)  # 限制影響範圍
            
            result['final_gl'] *= trend_factor
            result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        
        return result

class FormulaA4(AdvancedGlycemicCalculator):
    """A4公式 - 神經網絡啟發版（模擬LSTM）"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A4 - 神經網絡啟發版"
        self.version = "4.0"
        # 模擬LSTM的隱藏狀態
        self.hidden_state = [0.0, 0.0, 0.0]
        self.cell_state = [0.0, 0.0, 0.0]
    
    def lstm_like_prediction(self, current_input: List[float]) -> float:
        """模擬LSTM的預測機制"""
        # 簡化的LSTM門控機制
        forget_gate = [1.0 / (1.0 + math.exp(-x)) for x in current_input[:3]]  # sigmoid
        input_gate = [1.0 / (1.0 + math.exp(-x)) for x in current_input[:3]]
        output_gate = [1.0 / (1.0 + math.exp(-x)) for x in current_input[:3]]
        
        # 更新細胞狀態
        for i in range(3):
            candidate = math.tanh(current_input[i] if i < len(current_input) else 0)
            self.cell_state[i] = forget_gate[i] * self.cell_state[i] + input_gate[i] * candidate
            self.hidden_state[i] = output_gate[i] * math.tanh(self.cell_state[i])
        
        # 輸出預測
        return sum(self.hidden_state) / 3.0
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # 準備LSTM輸入特徵
        lstm_input = [
            food.total_carbs_g / 100.0,
            person.bmi / 30.0,
            person.hba1c / 10.0,
            len(meal_history.meals) / 10.0,
            context.fasting_hours / 12.0
        ]
        
        # LSTM預測調整
        lstm_adjustment = self.lstm_like_prediction(lstm_input)
        neural_factor = 1.0 + (lstm_adjustment * 0.15)
        
        result['final_gl'] *= neural_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        
        return result

class FormulaA5(AdvancedGlycemicCalculator):
    """A5公式 - Prophet趨勢預測版"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A5 - Prophet趨勢預測版"
        self.version = "5.0"
    
    def prophet_like_decomposition(self, meal_history: MealHistory, current_time: float) -> Dict[str, float]:
        """模擬Prophet的時間序列分解"""
        if len(meal_history.meals) < 5:
            return {'trend': 1.0, 'seasonal': 1.0, 'holiday': 1.0}
        
        # 趨勢組件（線性趨勢）
        recent_meals = meal_history.meals[-5:]
        if len(recent_meals) >= 2:
            trend_slope = (recent_meals[-1][1] - recent_meals[0][1]) / len(recent_meals)
            trend = 1.0 + (trend_slope * 0.01)
        else:
            trend = 1.0
        
        # 季節性組件（一日內的週期性）
        hour_of_day = current_time % 24
        seasonal = 1.0 + 0.1 * math.sin(2 * math.pi * hour_of_day / 24)
        
        # 假期效應（週末效應）
        day_of_week = int(current_time / 24) % 7
        holiday = 1.05 if day_of_week in [5, 6] else 1.0  # 週末稍高
        
        return {'trend': trend, 'seasonal': seasonal, 'holiday': holiday}
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # Prophet分解
        current_time = 12.0  # 假設當前時間
        decomposition = self.prophet_like_decomposition(meal_history, current_time)
        
        # 應用Prophet調整
        prophet_factor = (decomposition['trend'] * 
                         decomposition['seasonal'] * 
                         decomposition['holiday'])
        
        result['final_gl'] *= prophet_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']
        
        return result

class FormulaA6(AdvancedGlycemicCalculator):
    """A6公式 - Kalman濾波器版"""
    
    def __init__(self):
        super().__init__()
        self.formula_name = "A6 - Kalman濾波器版"
        self.version = "6.0"
        # Kalman濾波器狀態
        self.state_estimate = 20.0  # 初始GL估計
        self.error_covariance = 10.0  # 初始誤差協方差
        self.process_noise = 1.0  # 過程噪聲
        self.measurement_noise = 2.0  # 測量噪聲
    
    def kalman_filter_update(self, measurement: float) -> float:
        """Kalman濾波器更新"""
        # 預測步驟
        predicted_state = self.state_estimate
        predicted_covariance = self.error_covariance + self.process_noise
        
        # 更新步驟
        kalman_gain = predicted_covariance / (predicted_covariance + self.measurement_noise)
        self.state_estimate = predicted_state + kalman_gain * (measurement - predicted_state)
        self.error_covariance = (1 - kalman_gain) * predicted_covariance
        
        return self.state_estimate
    
    def calculate_comprehensive_glycemic_index(self, food: FoodComposition, 
                                             person: ExtendedPersonalProfile,
                                             context: MealContext, 
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        
        # Kalman濾波器調整
        filtered_gl = self.kalman_filter_update(result['final_gl'])
        kalman_factor = filtered_gl / max(result['final_gl'], 1.0)
        
        result['final_gl'] = filtered_gl
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']

        return result

class FormulaA7(AdvancedGlycemicCalculator):
    """A7公式 - 代謝狀態動態調整版"""

    def __init__(self):
        super().__init__()
        self.formula_name = "A7 - 代謝狀態動態調整版"
        self.version = "7.0"

    def calculate_metabolic_state_factor(self, person: ExtendedPersonalProfile, context: MealContext) -> float:
        """計算動態代謝狀態因子"""
        # 肝糖原狀態影響
        hepatic_factor = 0.5 + (person.hepatic_glycogen_pct / 100.0) * 0.5

        # 肌糖原狀態影響
        muscle_factor = 0.6 + (person.muscle_glycogen_pct / 100.0) * 0.4

        # 胰島素分泌速率
        insulin_factor = 0.7 + (person.insulin_secretion_rate * 0.3)

        # 胰高血糖素敏感性
        glucagon_factor = 1.0 + (person.glucagon_sensitivity - 1.0) * 0.2

        # 自主神經平衡
        autonomic_factor = 0.8 + (person.autonomic_balance * 0.2)

        # 炎症狀態（CRP, IL-6, TNF-α）
        inflammation_factor = 1.0 + ((person.crp_level + person.il6_level + person.tnf_alpha - 3.0) * 0.05)

        return hepatic_factor * muscle_factor * insulin_factor * glucagon_factor * autonomic_factor / inflammation_factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: ExtendedPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用代謝狀態調整
        metabolic_factor = self.calculate_metabolic_state_factor(person, context)
        result['final_gl'] *= metabolic_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']

        return result

class FormulaA8(AdvancedGlycemicCalculator):
    """A8公式 - 血管內皮功能整合版"""

    def __init__(self):
        super().__init__()
        self.formula_name = "A8 - 血管內皮功能整合版"
        self.version = "8.0"

    def calculate_vascular_factor(self, person: ExtendedPersonalProfile) -> float:
        """計算血管功能影響因子"""
        # 血管內皮功能
        endothelial_factor = 0.7 + (person.endothelial_function * 0.3)

        # 血壓影響（收縮壓和舒張壓）
        systolic_factor = 1.0 + max(0, person.blood_pressure_sys - 120) * 0.002
        diastolic_factor = 1.0 + max(0, person.blood_pressure_dia - 80) * 0.003

        # 心率變異性（通過靜息心率估算）
        hrv_factor = 1.0 + (70 - person.resting_hr) * 0.005

        # 交感神經活性
        sympathetic_factor = 1.0 + (person.sympathetic_activity - 1.0) * 0.1

        return endothelial_factor / (systolic_factor * diastolic_factor) * hrv_factor / sympathetic_factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: ExtendedPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用血管功能調整
        vascular_factor = self.calculate_vascular_factor(person)
        result['final_gl'] *= vascular_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']

        return result

class FormulaA9(AdvancedGlycemicCalculator):
    """A9公式 - 遺傳表觀遺傳整合版"""

    def __init__(self):
        super().__init__()
        self.formula_name = "A9 - 遺傳表觀遺傳整合版"
        self.version = "9.0"

    def calculate_genetic_factor(self, person: ExtendedPersonalProfile) -> float:
        """計算遺傳和表觀遺傳影響因子"""
        # 遺傳風險評分
        genetic_factor = 0.8 + (person.genetic_risk_score * 0.2)

        # 表觀遺傳年齡vs實際年齡
        epigenetic_age_diff = person.epigenetic_age - person.age
        epigenetic_factor = 1.0 + (epigenetic_age_diff * 0.01)

        # 家族糖尿病史
        family_history_factor = 1.1 if person.family_diabetes_history else 1.0

        # 種族因子（通過BMI閾值推估）
        ethnicity_factor = 1.05 if person.bmi > 23 else 1.0  # 亞洲人群BMI閾值較低

        return genetic_factor * epigenetic_factor * family_history_factor * ethnicity_factor

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: ExtendedPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 應用遺傳因子調整
        genetic_factor = self.calculate_genetic_factor(person)
        result['final_gl'] *= genetic_factor
        result['glycemic_rate'] = result['final_gl'] / result['peak_time_hours']

        return result

class FormulaA10(AdvancedGlycemicCalculator):
    """A10公式 - 終極整合版（所有創新技術）"""

    def __init__(self):
        super().__init__()
        self.formula_name = "A10 - 終極整合版"
        self.version = "10.0"

        # 整合所有子模塊
        self.microbiome_module = FormulaA2()
        self.arima_module = FormulaA3()
        self.lstm_module = FormulaA4()
        self.prophet_module = FormulaA5()
        self.kalman_module = FormulaA6()
        self.metabolic_module = FormulaA7()
        self.vascular_module = FormulaA8()
        self.genetic_module = FormulaA9()

    def calculate_eating_behavior_factor(self, person: ExtendedPersonalProfile) -> float:
        """計算進食行為影響因子"""
        # 進食速度影響
        eating_speed_factor = 1.0 + (person.eating_speed - 1.0) * 0.15

        # 咀嚼效率影響
        chewing_factor = 1.0 - (person.chewing_efficiency - 1.0) * 0.1

        # 用餐頻率影響
        frequency_factor = 1.0 + (person.meal_frequency - 3) * 0.05

        # 水合狀態影響
        hydration_factor = 0.9 + (person.hydration_level * 0.1)

        return eating_speed_factor * chewing_factor * frequency_factor * hydration_factor

    def ensemble_prediction(self, individual_results: List[float]) -> float:
        """集成學習預測"""
        # 加權平均（根據各模型的歷史表現）
        weights = [0.15, 0.12, 0.12, 0.12, 0.12, 0.12, 0.10, 0.10, 0.05]  # A2-A9的權重

        if len(individual_results) != len(weights):
            return sum(individual_results) / len(individual_results)

        weighted_sum = sum(result * weight for result, weight in zip(individual_results, weights))
        return weighted_sum

    def calculate_comprehensive_glycemic_index(self, food: FoodComposition,
                                             person: ExtendedPersonalProfile,
                                             context: MealContext,
                                             meal_history: MealHistory) -> Dict[str, float]:
        # 獲取基礎結果
        base_result = super().calculate_comprehensive_glycemic_index(food, person, context, meal_history)

        # 收集所有子模型的預測
        individual_predictions = []

        # A2: 微生物因子
        microbiome_factor = self.microbiome_module.calculate_microbiome_factor(person)
        individual_predictions.append(base_result['final_gl'] * microbiome_factor)

        # A3: ARIMA預測
        arima_result = self.arima_module.calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        individual_predictions.append(arima_result['final_gl'])

        # A4: LSTM預測
        lstm_result = self.lstm_module.calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        individual_predictions.append(lstm_result['final_gl'])

        # A5: Prophet預測
        prophet_result = self.prophet_module.calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        individual_predictions.append(prophet_result['final_gl'])

        # A6: Kalman濾波
        kalman_result = self.kalman_module.calculate_comprehensive_glycemic_index(food, person, context, meal_history)
        individual_predictions.append(kalman_result['final_gl'])

        # A7: 代謝狀態
        metabolic_factor = self.metabolic_module.calculate_metabolic_state_factor(person, context)
        individual_predictions.append(base_result['final_gl'] * metabolic_factor)

        # A8: 血管功能
        vascular_factor = self.vascular_module.calculate_vascular_factor(person)
        individual_predictions.append(base_result['final_gl'] * vascular_factor)

        # A9: 遺傳因子
        genetic_factor = self.genetic_module.calculate_genetic_factor(person)
        individual_predictions.append(base_result['final_gl'] * genetic_factor)

        # 集成預測
        ensemble_gl = self.ensemble_prediction(individual_predictions)

        # 應用進食行為調整
        eating_behavior_factor = self.calculate_eating_behavior_factor(person)
        final_gl = ensemble_gl * eating_behavior_factor

        # 更新結果
        result = base_result.copy()
        result['final_gl'] = final_gl
        result['glycemic_rate'] = final_gl / result['peak_time_hours']

        # 重新評估安全性
        safety_threshold = self._calculate_dynamic_safety_threshold(person, context)
        result['is_safe'] = result['glycemic_rate'] <= safety_threshold
        result['risk_level'] = self._assess_comprehensive_risk(result['glycemic_rate'], safety_threshold, person)

        # 添加集成信息
        result['individual_predictions'] = individual_predictions

        # 計算集成置信度（不使用numpy）
        if individual_predictions:
            mean_pred = sum(individual_predictions) / len(individual_predictions)
            variance = sum((x - mean_pred) ** 2 for x in individual_predictions) / len(individual_predictions)
            std_pred = variance ** 0.5
            result['ensemble_confidence'] = 1.0 - (std_pred / max(mean_pred, 1.0))
        else:
            result['ensemble_confidence'] = 0.5

        return result
