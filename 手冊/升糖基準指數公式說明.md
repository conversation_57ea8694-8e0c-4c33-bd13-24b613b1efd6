# 升糖基準指數公式說明

## 公式概述

本系統開發的「升糖基準指數」是一個綜合性的血糖升糖速率預測公式，基於多項科學研究，特別是Lee et al. (2021)在Foods期刊發表的GL預測模型，並擴展加入了個人化因素和進餐情境。

## 核心公式結構

### 第一層：基礎血糖負荷計算
基於Lee et al. (2021)的研究成果：

```
基礎GL = 19.27 + (0.39 × 可用碳水) - (0.21 × 脂肪) - (0.01 × 蛋白質²) - (0.01 × 纖維²)
```

其中：
- **可用碳水** = 總碳水含量 - 膳食纖維含量
- **係數解釋**：
  - 19.27：基礎常數項
  - 0.39：碳水化合物的正向影響係數
  - -0.21：脂肪的負向影響係數（延緩升糖）
  - -0.01：蛋白質平方的負向影響係數
  - -0.01：纖維平方的負向影響係數

### 第二層：食物特性修正
```
食物修正GL = 基礎GL × 碳水類型因子 × 加工等級因子 × 烹調方式因子 × 冷藏效應因子
```

#### 碳水類型修正因子
- **單醣** (simple_sugar): 1.3 - 快速吸收，升糖迅速
- **澱粉** (starch): 1.0 - 基準值
- **複合碳水** (complex_carb): 0.8 - 消化較慢，升糖緩和

#### 加工等級修正因子
- **生食** (raw): 0.8 - 最難消化
- **輕度加工** (minimal): 0.9 - 略易消化
- **中度加工** (moderate): 1.0 - 基準值
- **高度加工** (highly): 1.2 - 最易消化，升糖快

#### 烹調方式修正因子
- **生食** (raw): 0.8 - 消化最慢
- **蒸煮** (steamed): 0.9 - 溫和處理
- **水煮** (boiled): 1.0 - 基準值
- **烘烤/燒烤** (baked/grilled): 1.1 - 略增升糖
- **油炸** (fried): 1.3 - 高溫高油，升糖最快

#### 冷藏效應修正因子
- **有冷藏** (cooling_effect = True): 0.85 - 產生抗性澱粉
- **無冷藏** (cooling_effect = False): 1.0 - 基準值

### 第三層：個人化修正
```
最終GL = 食物修正GL × 代謝因子 × 晝夜節律因子 × 禁食時間因子
```

#### 代謝因子計算
```
代謝因子 = 年齡因子 × BMI因子 × 體脂因子
```

- **年齡因子** = 1.0 + (年齡 - 25) × 0.005
- **BMI因子** = 1.0 + max(0, BMI - 23) × 0.02
- **體脂因子** = 1.0 + max(0, 體脂率 - 15) × 0.01

#### 晝夜節律因子
- **早晨** (morning): 1.2 - 胰島素敏感性最高
- **中午** (midday): 1.0 - 基準值
- **下午** (afternoon): 0.9 - 略有下降
- **晚上** (evening): 0.8 - 敏感性降低
- **夜間** (night): 0.7 - 敏感性最低

#### 禁食時間因子
```
禁食時間因子 = 1.0 + max(0, 禁食小時數 - 8) × 0.02
```

### 第四層：升糖速率計算
```
升糖速率 = 最終GL / 2.0
```
假設在2小時內達到血糖峰值。

## 安全評估體系

### 個人化安全閾值
```
安全閾值 = 基礎閾值 × BMI修正 × 年齡修正
```

- **基礎閾值**: 15.0
- **BMI修正**: BMI > 25時 × 0.8
- **年齡修正**: 年齡 > 50時 × 0.9

### 風險等級評估
```
風險比例 = 升糖速率 / 安全閾值
```

- **低風險**: 比例 ≤ 0.7
- **中等風險**: 0.7 < 比例 ≤ 1.0
- **高風險**: 1.0 < 比例 ≤ 1.5
- **極高風險**: 比例 > 1.5

## 營養干預計算

### 干預需求計算
當升糖速率超過安全閾值時，系統計算所需的營養干預：

```
速率差異 = 當前升糖速率 - 目標升糖速率
```

#### 纖維干預
```
所需額外纖維 = 速率差異 / 0.75
```
基於研究，每克纖維約可降低GL 0.75

#### 蛋白質干預
```
所需額外蛋白質 = 速率差異 / 0.3
```
蛋白質的升糖抑制效果較溫和

#### 健康脂肪干預
```
所需額外健康脂肪 = 速率差異 / 0.4
```
健康脂肪可延緩胃排空，降低升糖速度

## 實際應用示例

### 示例1：白米飯（150g）
**食物參數**：
- 總碳水: 45g, 纖維: 0.5g, 蛋白質: 4g, 脂肪: 0.3g
- 澱粉類，中度加工，水煮，無冷藏

**個人參數**：
- 30歲，BMI 23，體脂15%，中午進食

**計算結果**：
- 基礎GL: 36.40
- 最終GL: 37.31
- 升糖速率: 18.65
- 風險等級: 高風險（超過安全閾值15.0）

**干預建議**：
- 額外纖維: 7.5g
- 額外蛋白質: 18.7g
- 額外健康脂肪: 14.0g

### 示例2：藜麥沙拉配堅果
**食物參數**：
- 總碳水: 35g, 纖維: 12g, 蛋白質: 18g, 脂肪: 15g
- 複合碳水，輕度加工，蒸煮，有冷藏

**計算結果**：
- 升糖速率: 8.33
- 風險等級: 中等風險（低於安全閾值）
- 無需額外干預

## 科學依據

### 主要參考文獻
1. **Lee et al. (2021)** - "Development of a Prediction Model to Estimate the Glycemic Load of Ready-to-Eat Meals", Foods 10(11):2626
2. **國際血糖指數標準** - ISO血糖指數測量協議
3. **多項餐後血糖反應研究** - 關於脂肪、蛋白質、纖維對血糖影響的研究

### 算法驗證
- 基於70種混合餐食的實驗數據
- 解釋力達73%，具統計顯著性
- 考慮了真實世界的複雜飲食情況

## 個人化校正機制

### 校正原理
系統收集用戶實際血糖測量數據，與預測值比較，動態調整個人化參數：

1. **誤差計算**: |預測GL - 實際峰值GL|
2. **參數調整**: 使用機器學習方法優化係數
3. **置信度評估**: 基於樣本數量和一致性
4. **持續改進**: 隨著數據增加，預測精度提升

### 校正參數
- 個人代謝係數
- 胰島素敏感性係數
- 食物偏好修正係數
- 生活習慣影響係數

## 系統優勢

1. **科學基礎**: 基於同行評議的研究成果
2. **個人化**: 考慮個體差異和生活習慣
3. **實用性**: 提供具體的營養干預建議
4. **自適應**: 通過實際數據持續優化
5. **全面性**: 涵蓋食物、個人、情境多個維度

這個升糖基準指數公式為糖尿病患者和健康管理者提供了一個科學、實用、個人化的血糖管理工具。
