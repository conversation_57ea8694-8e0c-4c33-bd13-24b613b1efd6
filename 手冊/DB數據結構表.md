# 數據庫結構設計

## 概述
升糖系統數據庫設計，用於存儲用戶資料、食物數據、血糖測量記錄和算法校正參數。

## 數據庫表結構

### 1. 用戶表 (users)
存儲用戶基本信息和生理參數

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| user_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 用戶唯一標識 |
| username | VARCHAR(50) | UNIQUE, NOT NULL | 用戶名 |
| email | VARCHAR(100) | UNIQUE, NOT NULL | 電子郵件 |
| password_hash | VARCHAR(255) | NOT NULL | 密碼哈希 |
| age | INTEGER | NOT NULL | 年齡 |
| weight_kg | DECIMAL(5,2) | NOT NULL | 體重(公斤) |
| height_cm | DECIMAL(5,2) | NOT NULL | 身高(公分) |
| bmi | DECIMAL(4,2) | COMPUTED | BMI值 |
| body_fat_pct | DECIMAL(4,2) | | 體脂率 |
| gender | ENUM('M','F','O') | NOT NULL | 性別 |
| diabetes_type | ENUM('none','prediabetes','type1','type2') | DEFAULT 'none' | 糖尿病類型 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新時間 |

### 2. 食物數據表 (foods)
存儲食物營養成分信息

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| food_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 食物唯一標識 |
| food_name | VARCHAR(100) | NOT NULL | 食物名稱 |
| food_name_en | VARCHAR(100) | | 英文名稱 |
| category | VARCHAR(50) | NOT NULL | 食物分類 |
| carb_type | ENUM('simple_sugar','starch','complex_carb') | NOT NULL | 碳水類型 |
| total_carbs_g | DECIMAL(6,2) | NOT NULL | 總碳水含量(克/100g) |
| fiber_g | DECIMAL(6,2) | DEFAULT 0 | 膳食纖維(克/100g) |
| protein_g | DECIMAL(6,2) | DEFAULT 0 | 蛋白質(克/100g) |
| fat_g | DECIMAL(6,2) | DEFAULT 0 | 脂肪(克/100g) |
| calories_per_100g | DECIMAL(6,2) | | 熱量(卡/100g) |
| processing_level | ENUM('raw','minimal','moderate','highly') | NOT NULL | 加工等級 |
| glycemic_index | DECIMAL(5,2) | | 標準升糖指數 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 3. 餐食記錄表 (meal_records)
存儲用戶的餐食記錄

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| meal_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 餐食記錄ID |
| user_id | INTEGER | FOREIGN KEY | 用戶ID |
| meal_date | DATE | NOT NULL | 用餐日期 |
| meal_time | TIME | NOT NULL | 用餐時間 |
| time_of_day | ENUM('morning','midday','afternoon','evening','night') | NOT NULL | 時段 |
| fasting_hours | DECIMAL(4,2) | DEFAULT 0 | 禁食小時數 |
| total_carbs_g | DECIMAL(6,2) | NOT NULL | 總碳水含量 |
| total_fiber_g | DECIMAL(6,2) | DEFAULT 0 | 總纖維含量 |
| total_protein_g | DECIMAL(6,2) | DEFAULT 0 | 總蛋白質含量 |
| total_fat_g | DECIMAL(6,2) | DEFAULT 0 | 總脂肪含量 |
| cooking_method | ENUM('raw','boiled','steamed','baked','fried','grilled') | | 主要烹調方式 |
| cooling_effect | BOOLEAN | DEFAULT FALSE | 是否冷藏過 |
| predicted_gl | DECIMAL(6,2) | | 預測血糖負荷 |
| predicted_rate | DECIMAL(6,2) | | 預測升糖速率 |
| notes | TEXT | | 備註 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 4. 餐食食物明細表 (meal_food_items)
存儲餐食中各種食物的詳細信息

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| item_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 明細ID |
| meal_id | INTEGER | FOREIGN KEY | 餐食記錄ID |
| food_id | INTEGER | FOREIGN KEY | 食物ID |
| portion_g | DECIMAL(6,2) | NOT NULL | 食用份量(克) |
| carbs_consumed | DECIMAL(6,2) | COMPUTED | 實際攝入碳水 |
| fiber_consumed | DECIMAL(6,2) | COMPUTED | 實際攝入纖維 |
| protein_consumed | DECIMAL(6,2) | COMPUTED | 實際攝入蛋白質 |
| fat_consumed | DECIMAL(6,2) | COMPUTED | 實際攝入脂肪 |

### 5. 血糖測量記錄表 (glucose_measurements)
存儲實際血糖測量數據

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| measurement_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 測量記錄ID |
| user_id | INTEGER | FOREIGN KEY | 用戶ID |
| meal_id | INTEGER | FOREIGN KEY | 關聯餐食ID |
| measurement_time | TIMESTAMP | NOT NULL | 測量時間 |
| minutes_after_meal | INTEGER | NOT NULL | 餐後分鐘數 |
| glucose_level | DECIMAL(5,2) | NOT NULL | 血糖值(mg/dL) |
| measurement_type | ENUM('fasting','postprandial','random') | NOT NULL | 測量類型 |
| device_type | VARCHAR(50) | | 測量設備 |
| notes | TEXT | | 備註 |

### 6. 算法校正參數表 (calibration_parameters)
存儲個人化算法校正參數

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| calibration_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 校正記錄ID |
| user_id | INTEGER | FOREIGN KEY | 用戶ID |
| parameter_name | VARCHAR(50) | NOT NULL | 參數名稱 |
| parameter_value | DECIMAL(8,4) | NOT NULL | 參數值 |
| confidence_score | DECIMAL(4,3) | DEFAULT 0.5 | 置信度分數 |
| sample_size | INTEGER | DEFAULT 1 | 樣本數量 |
| last_updated | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 最後更新時間 |

### 7. 營養干預建議表 (intervention_suggestions)
存儲系統生成的營養干預建議

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| suggestion_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 建議ID |
| meal_id | INTEGER | FOREIGN KEY | 餐食記錄ID |
| intervention_type | ENUM('fiber','protein','fat','timing','portion') | NOT NULL | 干預類型 |
| suggested_amount | DECIMAL(6,2) | | 建議數量 |
| suggested_food | VARCHAR(100) | | 建議食物 |
| expected_reduction | DECIMAL(4,3) | | 預期降低比例 |
| priority_level | ENUM('low','medium','high','critical') | DEFAULT 'medium' | 優先級 |
| status | ENUM('pending','accepted','rejected','completed') | DEFAULT 'pending' | 狀態 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 8. 用戶偏好設置表 (user_preferences)
存儲用戶個人偏好設置

| 欄位名 | 數據類型 | 約束 | 說明 |
|--------|----------|------|------|
| preference_id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | 偏好ID |
| user_id | INTEGER | FOREIGN KEY | 用戶ID |
| target_gl_threshold | DECIMAL(5,2) | DEFAULT 15.0 | 目標GL閾值 |
| alert_enabled | BOOLEAN | DEFAULT TRUE | 是否啟用警報 |
| preferred_units | ENUM('metric','imperial') | DEFAULT 'metric' | 偏好單位 |
| reminder_frequency | INTEGER | DEFAULT 3 | 提醒頻率(小時) |
| dietary_restrictions | JSON | | 飲食限制 |
| favorite_foods | JSON | | 常用食物列表 |

## 索引設計

### 主要索引
```sql
-- 用戶表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 餐食記錄索引
CREATE INDEX idx_meal_records_user_date ON meal_records(user_id, meal_date);
CREATE INDEX idx_meal_records_datetime ON meal_records(meal_date, meal_time);

-- 血糖測量索引
CREATE INDEX idx_glucose_user_time ON glucose_measurements(user_id, measurement_time);
CREATE INDEX idx_glucose_meal_minutes ON glucose_measurements(meal_id, minutes_after_meal);

-- 食物數據索引
CREATE INDEX idx_foods_name ON foods(food_name);
CREATE INDEX idx_foods_category ON foods(category);
CREATE INDEX idx_foods_carb_type ON foods(carb_type);

-- 校正參數索引
CREATE INDEX idx_calibration_user_param ON calibration_parameters(user_id, parameter_name);
```

## 視圖設計

### 1. 用戶血糖概覽視圖
```sql
CREATE VIEW user_glucose_overview AS
SELECT 
    u.user_id,
    u.username,
    AVG(gm.glucose_level) as avg_glucose,
    MAX(gm.glucose_level) as max_glucose,
    MIN(gm.glucose_level) as min_glucose,
    COUNT(gm.measurement_id) as total_measurements
FROM users u
LEFT JOIN glucose_measurements gm ON u.user_id = gm.user_id
WHERE gm.measurement_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.user_id, u.username;
```

### 2. 餐食效果分析視圖
```sql
CREATE VIEW meal_effectiveness AS
SELECT 
    mr.meal_id,
    mr.user_id,
    mr.predicted_gl,
    AVG(gm.glucose_level) as actual_avg_glucose,
    MAX(gm.glucose_level) as actual_peak_glucose,
    ABS(mr.predicted_gl - (MAX(gm.glucose_level) - MIN(gm.glucose_level))) as prediction_error
FROM meal_records mr
LEFT JOIN glucose_measurements gm ON mr.meal_id = gm.meal_id
WHERE gm.minutes_after_meal BETWEEN 30 AND 120
GROUP BY mr.meal_id, mr.user_id, mr.predicted_gl;
```

## 數據完整性約束

### 外鍵約束
```sql
ALTER TABLE meal_records ADD CONSTRAINT fk_meal_user 
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE meal_food_items ADD CONSTRAINT fk_item_meal 
    FOREIGN KEY (meal_id) REFERENCES meal_records(meal_id) ON DELETE CASCADE;

ALTER TABLE meal_food_items ADD CONSTRAINT fk_item_food 
    FOREIGN KEY (food_id) REFERENCES foods(food_id);

ALTER TABLE glucose_measurements ADD CONSTRAINT fk_glucose_user 
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE glucose_measurements ADD CONSTRAINT fk_glucose_meal 
    FOREIGN KEY (meal_id) REFERENCES meal_records(meal_id) ON DELETE SET NULL;
```

### 檢查約束
```sql
ALTER TABLE users ADD CONSTRAINT chk_age CHECK (age BETWEEN 1 AND 120);
ALTER TABLE users ADD CONSTRAINT chk_weight CHECK (weight_kg BETWEEN 20 AND 300);
ALTER TABLE users ADD CONSTRAINT chk_bmi CHECK (bmi BETWEEN 10 AND 50);

ALTER TABLE glucose_measurements ADD CONSTRAINT chk_glucose 
    CHECK (glucose_level BETWEEN 20 AND 600);

ALTER TABLE meal_records ADD CONSTRAINT chk_fasting_hours 
    CHECK (fasting_hours BETWEEN 0 AND 48);
```

## 數據初始化

### 基礎食物數據
系統將預載入常見食物的營養數據，包括：
- 主食類：米飯、麵條、麵包等
- 蛋白質類：肉類、蛋類、豆類等  
- 蔬菜類：各種蔬菜的營養成分
- 水果類：常見水果數據
- 加工食品：常見包裝食品數據

### 默認算法參數
系統將設置基於研究文獻的默認算法參數，用戶可通過實際測量數據進行個人化校正。
