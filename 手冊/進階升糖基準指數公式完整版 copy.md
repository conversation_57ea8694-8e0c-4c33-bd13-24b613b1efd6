# 進階升糖基準指數公式完整版

## 公式概述

基於國際多項研究（韓國、英國、美國、以色列等）的綜合升糖基準指數預測模型，整合了食物特性、個人生理參數、進餐情境、餐食疊加效應等多維度因素。

## 核心研究基礎

### 主要參考文獻
1. **<PERSON> et al. (2021)** - 韓國GL預測模型 (Foods期刊)
2. **<PERSON><PERSON><PERSON> et al. (2015)** - 以色列個人化血糖反應研究 (Cell期刊)
3. **<PERSON> et al. (2020)** - 英國PREDICT研究 (Nature Medicine)
4. **Mendes-Soares et al. (2019)** - 美國腸道微生物影響研究
5. **<PERSON>-<PERSON> et al. (2018)** - 胃排空速率研究
6. **<PERSON> et al. (1981)** - 原始GI概念研究

## 五層計算架構

### 第一層：增強基礎血糖負荷計算

```
增強基礎GL = 基礎GL + 糖分加成 + 纖維細分效應 + 電解質效應 + 稀釋效應
```

#### 基礎GL（基於Lee et al.）
```
基礎GL = 19.27 + (0.39 × 淨碳水) - (0.21 × 脂肪) - (0.01 × 蛋白質²) - (0.01 × 纖維²)
```

其中：`淨碳水 = (總碳水 - 纖維) × 抗性澱粉係數`

#### 增強因子
- **糖分加成** = 添加糖含量 × 0.15
- **可溶性纖維效應** = -可溶性纖維 × 0.3
- **不可溶性纖維效應** = -不可溶性纖維 × 0.15
- **電解質效應** = (鈉/鉀比例) × 0.5
- **稀釋效應** = -含水量% × 0.02

### 第二層：食物加工和烹調修正

```
烹調修正GL = 增強基礎GL × 烹調係數 × 時間係數 × 溫度係數 × 用油係數 × 冷藏係數
```

#### 碳水類型係數
- **單醣**: 1.4 (快速吸收)
- **雙醣**: 1.2 (中等吸收)
- **澱粉**: 1.0 (基準值)
- **複合碳水**: 0.7 (緩慢吸收)
- **抗性澱粉**: 0.4 (最慢吸收)

#### 加工等級係數
- **生食**: 0.6
- **輕度加工**: 0.8
- **中度加工**: 1.0
- **高度加工**: 1.3
- **超加工**: 1.6

#### 烹調方式係數
- **生食**: 0.7
- **蒸煮**: 0.85
- **水煮**: 1.0
- **烘烤**: 1.1
- **燒烤**: 1.15
- **炒**: 1.2
- **油炸**: 1.25
- **深度油炸**: 1.4
- **壓力鍋**: 1.3

#### 動態修正因子
- **時間係數** = 1.0 + (烹調時間 - 10分鐘) × 0.002
- **溫度係數** = 1.0 + max(0, 烹調溫度 - 100°C) × 0.001
- **用油係數** = 1.0 + 用油量 × 0.01
- **冷藏係數** = 0.75 (有冷藏) / 1.0 (無冷藏)

### 第三層：個人生理修正

```
個人修正GL = 烹調修正GL × 代謝因子 × 晝夜節律因子 × 環境因子
```

#### 胰島素敏感性指數計算
```
胰島素敏感性 = 基礎敏感性 × 年齡因子 × BMI因子 × 體脂因子 × 肌肉因子 × 
                生活習慣因子 × 疾病因子 × 藥物因子 × 運動因子
```

- **年齡因子** = 1.0 - (年齡 - 25) × 0.008
- **BMI因子** = 1.0 - max(0, BMI - 23) × 0.03
- **體脂因子** = 1.0 - max(0, 體脂率 - 15) × 0.02
- **肌肉因子** = 1.0 + (肌肉量 - 30kg) × 0.01
- **運動因子** = 1.0 + 運動頻率 × 0.05
- **睡眠因子** = 1.0 - |睡眠時間 - 7.5小時| × 0.1
- **壓力因子** = 1.0 - (壓力等級 - 1) × 0.05
- **糖尿病因子** = {無: 1.0, 前期: 0.8, 1型: 0.4, 2型: 0.6}
- **二甲雙胍因子** = 1.0 + 劑量 × 0.0001

#### 胃排空速率計算
```
胃排空速率 = 基礎速率 × 脂肪因子 × 纖維因子 × 蛋白質因子 × 黏稠度因子 × 年齡因子 × BMI因子
```

- **脂肪因子** = 1.0 - 脂肪含量 × 0.02
- **纖維因子** = 1.0 - 纖維含量 × 0.015
- **蛋白質因子** = 1.0 - 蛋白質含量 × 0.01
- **黏稠度因子** = 1.0 / 黏稠度係數
- **年齡因子** = 1.0 - (年齡 - 30) × 0.005
- **BMI因子** = 1.0 - max(0, BMI - 25) × 0.02

#### 代謝修正因子
```
代謝因子 = (2.0 - 胰島素敏感性) × 胃排空速率
```

#### 晝夜節律係數
- **清晨** (5-7): 1.3
- **早晨** (7-10): 1.2
- **上午** (10-12): 1.1
- **中午** (12-14): 1.0
- **下午** (14-17): 0.9
- **晚上** (17-20): 0.8
- **夜間** (20-23): 0.7
- **深夜** (23-5): 0.6

#### 環境因子
- **溫度因子** = 1.0 + (環境溫度 - 25°C) × 0.002
- **海拔因子** = 1.0 + 海拔高度 × 0.00001

### 第四層：餐食交互作用修正

```
交互作用GL = 個人修正GL × 累積效應因子 × 第二餐效應因子 × 禁食反彈因子
```

#### 累積效應計算
```
累積效應 = Σ(前餐GL × e^(-時間差/4小時))
累積效應因子 = 1.0 + 累積效應 × 0.01
```

#### 第二餐效應（Staub-Traugott效應）
- **< 2小時**: 0.7 (強烈抑制)
- **2-4小時**: 0.85 (中等抑制)
- **> 4小時**: 1.0 (無影響)

#### 禁食反彈效應
```
禁食反彈因子 = 1.0 + max(0, 禁食時間 - 12小時) × 0.01
```

#### 安全限制
```
最終交互作用GL = min(交互作用GL, 個人修正GL × 3.0)
```

### 第五層：藥物和補充劑修正

```
最終GL = 交互作用GL × α-葡萄糖苷酶抑制劑係數 × 二甲雙胍係數
```

- **α-葡萄糖苷酶抑制劑**: 0.7
- **二甲雙胍減少率** = min(0.3, 劑量 × 0.0002)

## 吸收動力學計算

### 營養素吸收速率
```
碳水吸收速率 = 胃排空速率 × 碳水類型係數 × 顆粒大小係數 × pH係數 × 蛋白質延緩係數 × 脂肪延緩係數
```

- **顆粒大小係數** = 2.0 - 顆粒大小
- **pH係數** = 1.0 + |pH值 - 6.5| × 0.05
- **蛋白質延緩係數** = 1.0 - 蛋白質含量 × 0.008
- **脂肪延緩係數** = 1.0 - 脂肪含量 × 0.012

### 時間參數計算
- **峰值時間** = 2.0 / 碳水吸收速率 (小時)
- **持續時間** = 4.0 / 碳水吸收速率 (小時)
- **升糖速率** = 最終GL / 峰值時間

## 動態安全閾值系統

### 個人化安全閾值
```
安全閾值 = 基礎閾值 × 糖尿病修正 × BMI修正 × 年齡修正 × HbA1c修正 × 時間修正 × 運動修正
```

- **基礎閾值**: 15.0
- **糖尿病修正**: 有糖尿病 × 0.7
- **BMI修正**: BMI > 25 × 0.85
- **年齡修正**: 年齡 > 50 × 0.9
- **HbA1c修正**: >6.5 × 0.6, >5.7 × 0.8
- **時間修正**: 夜間 × 0.8
- **運動修正**: 運動後2小時內 × 1.3

### 風險等級評估
```
風險比例 = 升糖速率 / 安全閾值
```

- **極低風險**: ≤ 0.6
- **低風險**: 0.6-0.8
- **中等風險**: 0.8-1.0
- **高風險**: 1.0-1.3
- **極高風險**: 1.3-1.8
- **危險**: > 1.8

## 綜合營養干預策略

### 纖維干預
- **可溶性纖維需求** = 速率差異 / 1.2
- **不可溶性纖維需求** = 速率差異 / 0.6

### 蛋白質干預
- **乳清蛋白需求** = 速率差異 / 0.4 (快速作用)
- **酪蛋白需求** = 速率差異 / 0.3 (緩慢釋放)

### 脂肪干預
- **Omega-3需求** = 速率差異 / 0.5
- **MCT油需求** = 速率差異 / 0.3

### 功能性補充劑
- **鉻**: 升糖速率 > 目標值1.2倍時，200微克
- **肉桂**: max(0, min(6.0, 速率差異 × 2))克
- **α-硫辛酸**: 升糖速率 > 目標值1.5倍時，200毫克

### 運動干預
根據速率差異和個人體能狀況：
- **< 5差異**: 輕度步行 10-20分鐘
- **5-15差異**: 快走或慢跑 15-35分鐘
- **> 15差異**: 間歇性運動 20-50分鐘

## 台灣美食測試結果

### 測試場景
35歲健康男性，BMI 24.2，一日六餐台灣美食測試

### 主要發現
1. **滷肉飯**: 升糖速率 8.3，極低風險
2. **珍珠奶茶**: 升糖速率 3.1，極低風險（第二餐效應）
3. **牛肉麵**: 升糖速率 6.3，極低風險
4. **雞排**: 升糖速率 2.3，極低風險
5. **炒飯**: 升糖速率 8.6，極低風險
6. **芒果冰**: 升糖速率 28.9，危險等級

### 餐食疊加效應
- 先吃滷肉飯後喝珍珠奶茶，升糖速率降低5.5%
- 證實第二餐效應（Staub-Traugott效應）的存在

## 公式優勢

1. **科學嚴謹**: 基於多國同行評議研究
2. **個人化**: 考慮個體差異和生活習慣
3. **動態調整**: 根據實際情況調整參數
4. **實用性**: 提供具體干預建議
5. **全面性**: 涵蓋食物、個人、情境、交互作用
6. **可校正**: 支持實際血糖數據校正優化

這個進階升糖基準指數公式為精準血糖管理提供了科學、實用、個人化的解決方案。
