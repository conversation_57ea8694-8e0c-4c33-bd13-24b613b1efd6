# 升糖系統項目需求書

## 1. 項目概述

### 1.1 項目背景
隨著糖尿病患者數量的持續增長，血糖管理成為重要的健康議題。現有的血糖指數(GI)和血糖負荷(GL)主要針對單一食物，缺乏對混合餐食和個人化因素的考慮。本項目旨在開發一個基於科學研究的升糖基準指數計算系統。

### 1.2 項目目標
- 開發精確的升糖基準指數計算算法
- 提供個人化的血糖管理建議
- 建立用戶友好的Web應用界面
- 實現血糖數據的追蹤和分析功能

## 2. 功能需求

### 2.1 核心功能模塊

#### 2.1.1 用戶管理模塊
**頁面**: 註冊/登入頁面
- **功能**: 用戶註冊、登入、個人資料管理
- **交互邏輯**: 
  - 註冊時收集基本生理參數（年齡、體重、身高、體脂率）
  - 糖尿病類型選擇（無、前期、1型、2型）
  - 個人偏好設置（目標GL閾值、提醒頻率）
- **涉及DB表**: users, user_preferences

#### 2.1.2 食物數據管理模塊
**頁面**: 食物數據庫頁面
- **功能**: 食物搜尋、營養成分查看、自定義食物添加
- **交互邏輯**:
  - 食物分類瀏覽（主食、蛋白質、蔬菜、水果等）
  - 營養成分詳細顯示
  - 用戶可添加自定義食物
- **涉及DB表**: foods

#### 2.1.3 餐食記錄模塊
**頁面**: 餐食記錄頁面
- **功能**: 記錄每餐食物、計算預測GL、營養干預建議
- **交互邏輯**:
  - 選擇食物和份量
  - 設定烹調方式和進餐時間
  - 實時計算升糖指數和風險等級
  - 顯示營養干預建議
- **涉及DB表**: meal_records, meal_food_items, intervention_suggestions

#### 2.1.4 血糖監測模塊
**頁面**: 血糖記錄頁面
- **功能**: 記錄實際血糖測量值、與預測值比較
- **交互邏輯**:
  - 選擇關聯的餐食記錄
  - 輸入測量時間和血糖值
  - 自動計算預測準確度
- **涉及DB表**: glucose_measurements

#### 2.1.5 數據分析模塊
**頁面**: 分析報告頁面
- **功能**: 血糖趨勢分析、算法準確度評估、個人化校正
- **交互邏輯**:
  - 顯示血糖變化趨勢圖表
  - 分析不同食物的升糖效果
  - 算法準確度統計
  - 個人化參數調整建議
- **涉及DB表**: calibration_parameters, 各種分析視圖

### 2.2 頁面結構詳細說明

#### 2.2.1 主頁面 (Dashboard)
- **佈局**: 響應式設計，支持桌面和移動設備
- **內容**:
  - 今日血糖概覽
  - 最近餐食記錄
  - 風險警報提醒
  - 快速記錄入口

#### 2.2.2 餐食記錄頁面
- **列表頁**: 顯示歷史餐食記錄，支持篩選和搜尋
- **編輯頁**: 新增/編輯餐食記錄
  - 食物選擇器（搜尋、分類瀏覽）
  - 份量輸入（支持常見單位轉換）
  - 烹調方式選擇
  - 進餐時間設定
- **彈出窗口**: 
  - 升糖指數計算結果顯示
  - 營養干預建議詳情
  - 食物營養成分詳情

#### 2.2.3 血糖記錄頁面
- **列表頁**: 血糖測量歷史，支持時間範圍篩選
- **編輯頁**: 新增血糖測量記錄
  - 測量時間選擇
  - 血糖值輸入（支持不同單位）
  - 關聯餐食選擇
  - 測量情境備註

#### 2.2.4 分析報告頁面
- **趨勢圖表**: 血糖變化趨勢、餐後血糖曲線
- **統計數據**: 平均血糖、峰值統計、風險事件統計
- **準確度分析**: 預測vs實際血糖比較
- **個人化建議**: 基於數據的個人化參數調整建議

## 3. 業務邏輯說明

### 3.1 升糖指數計算邏輯
1. **基礎計算**: 基於Lee et al.(2021)公式
   - GL = 19.27 + (0.39 × 可用碳水) - (0.21 × 脂肪) - (0.01 × 蛋白質²) - (0.01 × 纖維²)

2. **食物修正因子**:
   - 碳水類型: 單醣(1.3) > 澱粉(1.0) > 複合碳水(0.8)
   - 加工等級: 高度加工(1.2) > 中度(1.0) > 輕度(0.9) > 生食(0.8)
   - 烹調方式: 油炸(1.3) > 烘烤/燒烤(1.1) > 水煮(1.0) > 蒸煮(0.9) > 生食(0.8)
   - 冷藏效應: 有抗性澱粉(0.85) vs 無(1.0)

3. **個人化修正因子**:
   - 年齡因子: 1.0 + (年齡-25) × 0.005
   - BMI因子: 1.0 + max(0, BMI-23) × 0.02
   - 體脂因子: 1.0 + max(0, 體脂率-15) × 0.01
   - 晝夜節律: 早晨(1.2) > 中午(1.0) > 下午(0.9) > 晚上(0.8) > 夜間(0.7)

### 3.2 風險評估邏輯
- **安全閾值計算**: 基礎15.0，根據BMI和年齡調整
- **風險等級**:
  - 低風險: ≤ 70%閾值
  - 中等風險: 70%-100%閾值
  - 高風險: 100%-150%閾值
  - 極高風險: >150%閾值

### 3.3 營養干預邏輯
- **纖維干預**: 每克纖維降低GL約0.75
- **蛋白質干預**: 每克蛋白質降低GL約0.3
- **健康脂肪干預**: 每克脂肪降低GL約0.4
- **目標**: 將升糖速率降低30-50%至安全範圍

### 3.4 個人化校正邏輯
- **數據收集**: 收集用戶實際血糖測量數據
- **誤差分析**: 計算預測值與實際值的偏差
- **參數調整**: 使用機器學習方法優化個人化係數
- **置信度評估**: 基於樣本數量和一致性評估參數可靠性

## 4. 數據字典

### 4.1 關鍵欄位說明
- **carb_type**: 碳水類型 (1=單醣, 2=澱粉, 3=複合碳水)
- **processing_level**: 加工等級 (1=生食, 2=輕度, 3=中度, 4=高度)
- **cooking_method**: 烹調方式 (1=生食, 2=水煮, 3=蒸煮, 4=烘烤, 5=油炸, 6=燒烤)
- **time_of_day**: 進食時段 (1=早晨, 2=中午, 3=下午, 4=晚上, 5=夜間)
- **diabetes_type**: 糖尿病類型 (0=無, 1=前期, 2=1型, 3=2型)
- **risk_level**: 風險等級 (1=低風險, 2=中等, 3=高風險, 4=極高風險)

### 4.2 計算欄位
- **available_carbs**: 可用碳水 = 總碳水 - 纖維
- **bmi**: BMI = 體重(kg) / 身高(m)²
- **metabolic_factor**: 代謝因子 = 年齡因子 × BMI因子 × 體脂因子
- **prediction_error**: 預測誤差 = |預測GL - 實際峰值GL|

## 5. 非功能需求

### 5.1 性能需求
- 升糖指數計算響應時間 < 1秒
- 頁面加載時間 < 3秒
- 支持並發用戶數 > 100

### 5.2 安全需求
- 用戶密碼加密存儲
- 個人健康數據隱私保護
- API接口安全驗證

### 5.3 可用性需求
- 直觀的用戶界面設計
- 支持多種設備（桌面、平板、手機）
- 多語言支持（繁體中文、英文）

### 5.4 可維護性需求
- 模塊化代碼結構
- 完整的API文檔
- 自動化測試覆蓋率 > 80%
