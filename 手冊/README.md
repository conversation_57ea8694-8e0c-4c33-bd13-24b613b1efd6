# 升糖系統 - 血糖升糖基準指數計算系統

## 項目概述
本項目旨在開發一個基於科學研究的「升糖基準指數」計算系統，用於預測食物攝入後的血糖升糖速率，並提供營養干預建議以控制血糖在安全範圍內。

## 核心功能
1. **升糖基準指數計算** - 基於食物營養成分和個人生理參數計算升糖速率
2. **營養干預建議** - 計算需要的蛋白質、纖維、不飽和脂肪等來降低升糖速度
3. **個人化校正** - 通過實際血糖測量數據優化公式參數
4. **安全監控** - 確保血糖升糖速度維持在安全值範圍內

## 技術架構
- **後端**: Python Flask/FastAPI
- **前端**: React/Vue.js
- **數據庫**: SQLite/MySQL
- **算法**: 基於多元回歸和機器學習的預測模型

## 項目結構
```
升糖系統/
├── 手冊/                    # 項目文檔
│   ├── README.md            # 項目說明
│   ├── 開發規則書.md         # 開發規範
│   ├── DB數據結構表.md       # 數據庫設計
│   ├── 網站樹狀結構.md       # 網站架構
│   ├── 後台API介接說明書.md   # API文檔
│   ├── 開發進度報告.md       # 進度追蹤
│   ├── 項目需求書.md         # 需求規格
│   └── 工作日誌/            # 開發日誌
├── backend/                 # 後端代碼
├── frontend/                # 前端代碼
├── database/                # 數據庫文件
├── algorithms/              # 算法模塊
├── tests/                   # 測試文件
└── docs/                    # 技術文檔
```

## 核心算法參數

### 食物參數
- `carb_type`: 碳水類型（單醣、澱粉）
- `total_carbs_g`: 總碳水含量（克）
- `fiber_g`: 膳食纖維（克）
- `protein_g`: 蛋白質（克）
- `fat_g`: 脂肪（克）
- `processing_level`: 加工等級
- `cooking_method`: 烹調方式
- `cooling_effect`: 冷藏效應

### 人體參數
- `age`: 年齡
- `weight_kg`: 體重
- `bmi`: 身體質量指數
- `body_fat_pct`: 體脂率

### 行為參數
- `time_of_day`: 進食時間

## 開發狀態
- [x] 項目初始化
- [x] 文檔結構建立
- [ ] 算法研究與實現
- [ ] 數據庫設計
- [ ] 後端開發
- [ ] 前端開發
- [ ] 測試與優化

## 更新記錄
- 2024-01-XX: 項目初始化，建立基礎文檔結構
