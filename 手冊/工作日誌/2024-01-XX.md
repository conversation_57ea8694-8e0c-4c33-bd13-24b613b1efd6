# 工作日誌 - 2024年1月XX日

## 系統時間
開始時間: 2024-01-XX 14:30:00

## 今日工作內容

### 1. 項目初始化 (14:30-15:00)
- 建立項目基礎結構
- 創建手冊目錄和核心文檔
- 設定README.md基礎內容

### 2. 研究文獻調研 (15:00-16:30)
- 搜尋並分析血糖指數相關研究論文
- 重點研究MDPI Foods期刊的GL預測模型論文
- 發現關鍵公式: GL = 19.27 + (0.39 × available carbohydrate) – (0.21 × fat) – (0.01 × protein²) – (0.01 × fiber²)

### 3. 算法設計規劃 (16:30-17:00)
- 分析現有研究中的預測公式
- 規劃升糖基準指數的核心參數
- 設計個人化校正機制

## 技術發現

### 關鍵研究成果
1. **韓國研究團隊的GL預測公式** (<PERSON> et al., 2021):
   - 基於70種混合餐食的實驗數據
   - 考慮了碳水化合物、脂肪、蛋白質、纖維的影響
   - 解釋力達73%，具有統計顯著性

2. **影響因子分析**:
   - 可用碳水化合物: 正相關 (係數 0.39)
   - 脂肪含量: 負相關 (係數 -0.21)
   - 蛋白質²: 負相關 (係數 -0.01)
   - 纖維²: 負相關 (係數 -0.01)

### 算法優化方向
1. 增加個人生理參數 (年齡、BMI、體脂率)
2. 考慮進食時間的影響
3. 加入食物加工和烹調方式的修正因子
4. 建立個人化校正機制

## 下一步計劃
1. 建立完整的數據庫結構設計
2. 實現基礎的升糖指數計算算法
3. 設計用戶界面原型
4. 準備測試數據集

## 問題與挑戰
1. 如何整合多個影響因子到統一公式中
2. 個人化參數的權重如何確定
3. 實時校正機制的實現方式

## 技術實現
- 使用Python進行算法實現
- 考慮使用機器學習方法優化預測精度
- 建立SQLite數據庫存儲用戶數據和校正記錄
